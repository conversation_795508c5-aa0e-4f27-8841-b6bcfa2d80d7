import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../core/utils/currency_utils.dart';
import '../../providers/cart_provider.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/custom_icon_widget.dart';

class CartScreen extends StatelessWidget {
  const CartScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: 'Shopping Cart',
        variant: CustomAppBarVariant.minimal,
        automaticallyImplyLeading: true,
      ),
      body: Consumer<CartProvider>(
        builder: (context, cart, child) {
          if (cart.items.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomIconWidget(
                    iconName: 'shopping_cart',
                    color: AppTheme.lightTheme.colorScheme.outline,
                    size: 48,
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    'Your cart is empty',
                    style: AppTheme.lightTheme.textTheme.titleMedium,
                  ),
                  SizedBox(height: 1.h),
                  Text(
                    'Add items from the store',
                    style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurface
                          .withOpacity(0.6),
                    ),
                  ),
                  SizedBox(height: 2.h),
                  ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Continue Shopping'),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              Expanded(
                child: ListView.separated(
                  padding: EdgeInsets.all(4.w),
                  itemCount: cart.items.length,
                  separatorBuilder: (context, index) => SizedBox(height: 2.h),
                  itemBuilder: (context, index) {
                    final item = cart.items.values.toList()[index];
                    return Card(
                      child: Padding(
                        padding: EdgeInsets.all(3.w),
                        child: Row(
                          children: [
                            Container(
                              width: 20.w,
                              height: 20.w,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(2.w),
                                image: DecorationImage(
                                  image: AssetImage(item.image),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                            SizedBox(width: 3.w),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    item.name,
                                    style: AppTheme.lightTheme.textTheme.titleMedium,
                                  ),
                                  SizedBox(height: 1.h),
                                  Text(
                                    formatNaira(item.price),
                                    style: AppTheme.lightTheme.textTheme.bodyLarge?.copyWith(
                                      fontFamily: 'IBMPlexMono',
                                      color: AppTheme.lightTheme.colorScheme.primary,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Column(
                              children: [
                                IconButton(
                                  onPressed: () {
                                    cart.addItem(
                                      productId: item.productId,
                                      name: item.name,
                                      price: item.price,
                                      image: item.image,
                                    );
                                  },
                                  icon: CustomIconWidget(
                                    iconName: 'add_circle',
                                    color: AppTheme.lightTheme.colorScheme.secondary,
                                    size: 24,
                                  ),
                                ),
                                Text(
                                  '${item.quantity}',
                                  style: AppTheme.lightTheme.textTheme.titleMedium,
                                ),
                                IconButton(
                                  onPressed: () {
                                    cart.removeSingleItem(item.productId);
                                  },
                                  icon: CustomIconWidget(
                                    iconName: 'remove_circle',
                                    color: AppTheme.lightTheme.colorScheme.error,
                                    size: 24,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
              Container(
                padding: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: AppTheme.lightTheme.colorScheme.surface,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, -5),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Total',
                          style: AppTheme.lightTheme.textTheme.titleLarge,
                        ),
                        Text(
                          formatNaira(cart.totalAmount),
                          style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                            fontFamily: 'IBMPlexMono',
                            color: AppTheme.lightTheme.colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 2.h),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          // TODO: Implement checkout
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Proceeding to checkout...'),
                            ),
                          );
                        },
                        child: const Text('Proceed to Checkout'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
        builder: (context, cart, child) {
          if (cart.items.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomIconWidget(
                    iconName: 'shopping_cart',
                    color: AppTheme.lightTheme.colorScheme.outline,
                    size: 48,
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    'Your cart is empty',
                    style: AppTheme.lightTheme.textTheme.titleMedium,
                  ),
                  SizedBox(height: 1.h),
                  Text(
                    'Add items from the store',
                    style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurface
                          .withValues(alpha: 0.6),
                    ),
                  ),
                  SizedBox(height: 2.h),
                  ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text('Continue Shopping'),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              Expanded(
                child: ListView.separated(
                  padding: EdgeInsets.all(4.w),
                  itemCount: cart.items.length,
                  separatorBuilder: (context, index) => SizedBox(height: 2.h),
                  itemBuilder: (context, index) {
                    final item = cart.items.values.toList()[index];
                    return Card(
                      child: Padding(
                        padding: EdgeInsets.all(3.w),
                        child: Row(
                          children: [
                            Container(
                              width: 20.w,
                              height: 20.w,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(2.w),
                                image: DecorationImage(
                                  image: AssetImage(item.image),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                            SizedBox(width: 3.w),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    item.name,
                                    style: AppTheme.lightTheme.textTheme.titleMedium,
                                  ),
                                  SizedBox(height: 1.h),
                                  Text(
                                    formatNaira(item.price),
                                    style: AppTheme.lightTheme.textTheme.bodyLarge
                                        ?.copyWith(
                                      fontFamily: 'IBMPlexMono',
                                      color: AppTheme.lightTheme.colorScheme.primary,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Column(
                              children: [
                                IconButton(
                                  onPressed: () {
                                    cart.addItem(
                                      productId: item.productId,
                                      name: item.name,
                                      price: item.price,
                                      image: item.image,
                                    );
                                  },
                                  icon: CustomIconWidget(
                                    iconName: 'add_circle',
                                    color:
                                        AppTheme.lightTheme.colorScheme.secondary,
                                    size: 24,
                                  ),
                                ),
                                Text(
                                  '${item.quantity}',
                                  style:
                                      AppTheme.lightTheme.textTheme.titleMedium,
                                ),
                                IconButton(
                                  onPressed: () {
                                    cart.removeSingleItem(item.productId);
                                  },
                                  icon: CustomIconWidget(
                                    iconName: 'remove_circle',
                                    color: AppTheme.lightTheme.colorScheme.error,
                                    size: 24,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
              Container(
                padding: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: AppTheme.lightTheme.colorScheme.surface,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: Offset(0, -5),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Total',
                          style: AppTheme.lightTheme.textTheme.titleLarge,
                        ),
                        Text(
                          formatNaira(cart.totalAmount),
                          style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                            fontFamily: 'IBMPlexMono',
                            color: AppTheme.lightTheme.colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 2.h),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          // TODO: Implement checkout
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Proceeding to checkout...'),
                            ),
                          );
                        },
                        child: Text('Proceed to Checkout'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
        ),
      ),
    );
  }
}
