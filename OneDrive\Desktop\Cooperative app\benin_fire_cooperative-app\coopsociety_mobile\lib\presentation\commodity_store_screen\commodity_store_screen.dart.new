import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/app_export.dart';
import '../../core/types.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/custom_bottom_bar.dart';
import '../../widgets/custom_icon_widget.dart';
import 'widgets/category_filter_chip_widget.dart';
import 'widgets/empty_state_widget.dart';
import 'widgets/product_card_widget.dart';
import 'widgets/quick_actions_bottom_sheet_widget.dart';
import 'widgets/admin_upload_commodity_widget.dart';
import 'widgets/search_bar_widget.dart';
import 'widgets/skeleton_loader_widget.dart';
import 'widgets/sort_bottom_sheet_widget.dart';

class CommodityStoreScreen extends StatefulWidget {
  const CommodityStoreScreen({super.key});

  @override
  State<CommodityStoreScreen> createState() => _CommodityStoreScreenState();
}

class _CommodityStoreScreenState extends State<CommodityStoreScreen> with TickerProviderStateMixin {
  // For demo: set to true to show admin features
  final bool isAdmin = false;
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // State variables
  String _selectedCategory = 'All';
  SortOption _currentSort = SortOption.newest;
  bool _isLoading = false;
  bool _isLoadingMore = false;
  int _cartItemCount = 0;
  List<String> _recentSearches = ['Seeds', 'Fertilizer', 'Tools'];
  List<String> _categories = ['All', 'Food'];
  List<Map<String, dynamic>> _filteredProducts = [];

  // Product list for BAFSC
  final List<Map<String, dynamic>> _allProducts = [
    {
      "id": 1,
      "name": "Bag of Rice",
      "category": "Food",
      "price": 55000,
      "stock": 100,
      "image": "",
      "description": "50kg bag of premium Nigerian rice.",
      "dateAdded": DateTime.now().subtract(const Duration(days: 1)),
    },
    {
      "id": 2,
      "name": "Cow Meat",
      "category": "Food",
      "price": 3500,
      "stock": 50,
      "image": "",
      "description": "Fresh cow meat per kg.",
      "dateAdded": DateTime.now().subtract(const Duration(days: 2)),
    },
    {
      "id": 3,
      "name": "Indomie Noddles in Cartons",
      "category": "Food",
      "price": 9000,
      "stock": 80,
      "image": "",
      "description": "Carton of Indomie noodles (40 packs).",
      "dateAdded": DateTime.now().subtract(const Duration(days: 3)),
    },
    {
      "id": 4,
      "name": "Vegetable Oil 5 liters available in Cartons",
      "category": "Food",
      "price": 42000,
      "stock": 30,
      "image": "",
      "description": "Carton of 5L vegetable oil (4 bottles).",
      "dateAdded": DateTime.now().subtract(const Duration(days: 4)),
    },
    {
      "id": 5,
      "name": "Vegetable Oil 1 liters available in Cartons",
      "category": "Food",
      "price": 11000,
      "stock": 40,
      "image": "",
      "description": "Carton of 1L vegetable oil (12 bottles).",
      "dateAdded": DateTime.now().subtract(const Duration(days: 5)),
    },
    {
      "id": 6,
      "name": "Tomatoe Paste In Cartons",
      "category": "Food",
      "price": 8000,
      "stock": 60,
      "image": "",
      "description": "Carton of tomato paste (48 sachets).",
      "dateAdded": DateTime.now().subtract(const Duration(days: 6)),
    },
    {
      "id": 7,
      "name": "Okomu Oil available in Catorn",
      "category": "Food",
      "price": 48000,
      "stock": 20,
      "image": "",
      "description": "Carton of Okomu oil (4 x 5L bottles).",
      "dateAdded": DateTime.now().subtract(const Duration(days: 7)),
    },
  ];

  @override
  void initState() {
    super.initState();
    _filteredProducts = List.from(_allProducts);
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreProducts();
    }
  }

  void _loadMoreProducts() {
    if (_isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    // Simulate loading more products
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    });
  }

  void _filterProducts() {
    setState(() {
      _filteredProducts = _allProducts.where((product) {
        final matchesCategory = _selectedCategory == 'All' ||
            (product['category'] as String) == _selectedCategory;
        final matchesSearch = _searchController.text.isEmpty ||
            (product['name'] as String)
                .toLowerCase()
                .contains(_searchController.text.toLowerCase()) ||
            (product['category'] as String)
                .toLowerCase()
                .contains(_searchController.text.toLowerCase());

        return matchesCategory && matchesSearch;
      }).toList();

      _sortProducts();
    });
  }

  void _sortProducts() {
    setState(() {
      switch (_currentSort) {
        case SortOption.priceAsc:
          _filteredProducts.sort((a, b) => (a['price'] as int).compareTo(b['price'] as int));
          break;
        case SortOption.priceDesc:
          _filteredProducts.sort((a, b) => (b['price'] as int).compareTo(a['price'] as int));
          break;
        case SortOption.nameAsc:
          _filteredProducts.sort(
              (a, b) => (a['name'] as String).compareTo(b['name'] as String));
          break;
        case SortOption.nameDesc:
          _filteredProducts.sort(
              (a, b) => (b['name'] as String).compareTo(a['name'] as String));
          break;
        case SortOption.stockAsc:
          _filteredProducts
              .sort((a, b) => (a['stock'] as int).compareTo(b['stock'] as int));
          break;
        case SortOption.stockDesc:
          _filteredProducts
              .sort((a, b) => (b['stock'] as int).compareTo(a['stock'] as int));
          break;
        case SortOption.newest:
          _filteredProducts.sort((a, b) => (b['dateAdded'] as DateTime)
              .compareTo(a['dateAdded'] as DateTime));
          break;
        case SortOption.oldest:
          _filteredProducts.sort((a, b) => (a['dateAdded'] as DateTime)
              .compareTo(b['dateAdded'] as DateTime));
          break;
      }
    });
  }

  void _onCategorySelected(String category) {
    setState(() {
      _selectedCategory = category;
    });
    _filterProducts();
  }

  void _onCategoryLongPress() {
    setState(() {
      _selectedCategory = 'All';
    });
    _filterProducts();
  }

  void _onSearchChanged(String query) {
    _filterProducts();
  }

  void _onVoiceSearch() {
    // Simulate voice search
    HapticFeedback.lightImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Voice search activated'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _onSearchClear() {
    _searchController.clear();
    _filterProducts();
  }

  void _onRecentSearchTap(String search) {
    _searchController.text = search;
    _filterProducts();
  }

  void _showAdminUploadDialog() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => AdminUploadCommodityWidget(
        onCommodityUploaded: (commodity) {
          setState(() {
            _allProducts.add(commodity);
            _filteredProducts = List.from(_allProducts);
          });
        },
      ),
    );
    if (result != null) {
      setState(() {
        _allProducts.add(result);
        _filteredProducts = List.from(_allProducts);
      });
    }
  }

  void _onProductTap(Map<String, dynamic> product) {
    QuickActionsBottomSheetWidget.show(
      context,
      product: product,
      onAddToCart: () => _addToCart(product),
      onViewDetails: () => _showProductDetails(product),
      onShare: () => _shareProduct(product),
      onSubmitForApproval: () => _submitForApproval(product),
    );
  }

  void _showProductDetails(Map<String, dynamic> product) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${product['name']}'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _submitForApproval(Map<String, dynamic> product) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Request for ${product['name']} submitted for approval!'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _onProductLongPress(Map<String, dynamic> product) {
    HapticFeedback.mediumImpact();
    QuickActionsBottomSheetWidget.show(
      context,
      product: product,
      onAddToCart: () => _addToCart(product),
      onViewDetails: () => _onProductTap(product),
      onShare: () => _shareProduct(product),
      onSubmitForApproval: () => _submitForApproval(product),
    );
  }

  void _addToCart(Map<String, dynamic> product) {
    final stock = (product['stock'] as int?) ?? 0;
    if (stock > 0) {
      setState(() {
        _cartItemCount++;
      });
      HapticFeedback.lightImpact();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${product['name']} added to cart'),
          duration: const Duration(seconds: 2),
          action: SnackBarAction(
            label: 'View Cart',
            onPressed: _viewCart,
          ),
        ),
      );
    }
  }

  void _shareProduct(Map<String, dynamic> product) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Sharing ${product['name']}'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _viewCart() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening shopping cart ($_cartItemCount items)'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _onSortChanged(SortOption sort) {
    setState(() {
      _currentSort = sort;
    });
    _sortProducts();
  }

  Future<void> _onRefresh() async {
    HapticFeedback.lightImpact();
    setState(() {
      _isLoading = true;
    });

    // Simulate refresh
    await Future.delayed(const Duration(seconds: 1));

    if (mounted) {
      setState(() {
        _isLoading = false;
        _filteredProducts = List.from(_allProducts);
      });
      _sortProducts();
    }
  }

  int _getCategoryCount(String category) {
    if (category == 'All') return _allProducts.length;
    return _allProducts
        .where((product) => (product['category'] as String) == category)
        .length;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Commodity Store',
        variant: CustomAppBarVariant.primary,
        actions: [
          if (isAdmin)
            IconButton(
              icon: Icon(Icons.add_box_rounded, color: AppTheme.lightTheme.colorScheme.primary),
              tooltip: 'Admin: Upload Commodity',
              onPressed: _showAdminUploadDialog,
            ),
          IconButton(
            onPressed: () {
              SortBottomSheetWidget.show(
                context,
                currentSort: _currentSort,
                onSortChanged: _onSortChanged,
              );
            },
            icon: CustomIconWidget(
              iconName: 'sort',
              color: colorScheme.onPrimary,
              size: 24,
            ),
          ),
          Stack(
            children: [
              IconButton(
                onPressed: _viewCart,
                icon: CustomIconWidget(
                  iconName: 'shopping_cart',
                  color: colorScheme.onPrimary,
                  size: 24,
                ),
              ),
              if (_cartItemCount > 0)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      '$_cartItemCount',
                      style: GoogleFonts.inter(
                        color: Colors.white,
                        fontSize: 10.sp,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          SearchBarWidget(
            controller: _searchController,
            onChanged: _onSearchChanged,
            onVoiceSearch: _onVoiceSearch,
            onClear: _onSearchClear,
            recentSearches: _recentSearches,
            onRecentSearchTap: _onRecentSearchTap,
          ),
          // Category Filter Chips
          Container(
            height: 60,
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                return CategoryFilterChipWidget(
                  category: category,
                  isSelected: _selectedCategory == category,
                  count: _getCategoryCount(category),
                  onTap: () => _onCategorySelected(category),
                  onLongPress: _onCategoryLongPress,
                );
              },
            ),
          ),
          // Products Grid
          Expanded(
            child: _isLoading
                ? const SkeletonLoaderWidget()
                : _filteredProducts.isEmpty
                    ? EmptyStateWidget(
                        title: _searchController.text.isNotEmpty
                            ? 'No products found'
                            : 'No products available',
                        subtitle: _searchController.text.isNotEmpty
                            ? 'Try adjusting your search or browse categories'
                            : 'Check back later for new products',
                        iconName: _searchController.text.isNotEmpty
                            ? 'search_off'
                            : 'inventory_2',
                        suggestions: const ['Seeds', 'Tools', 'Fertilizer', 'Grains'],
                        categoryShortcuts: const [
                          'Grains',
                          'Tools',
                          'Fertilizers',
                          'Seeds'
                        ],
                        onSuggestionTap: (suggestion) {
                          _searchController.text = suggestion;
                          _filterProducts();
                        },
                        onCategoryTap: (category) {
                          _onCategorySelected(category);
                        },
                      )
                    : RefreshIndicator(
                        onRefresh: _onRefresh,
                        child: GridView.builder(
                          controller: _scrollController,
                          padding: const EdgeInsets.all(16.0),
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            childAspectRatio: 0.75,
                            crossAxisSpacing: 12.0,
                            mainAxisSpacing: 12.0,
                          ),
                          itemCount: _filteredProducts.length +
                              (_isLoadingMore ? 2 : 0),
                          itemBuilder: (context, index) {
                            if (index >= _filteredProducts.length) {
                              return Container(
                                decoration: BoxDecoration(
                                  color: colorScheme.surface,
                                  borderRadius: BorderRadius.circular(12.0),
                                ),
                                child: const Center(
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2.0,
                                  ),
                                ),
                              );
                            }

                            final product = _filteredProducts[index];
                            return ProductCardWidget(
                              product: product,
                              onTap: () => _onProductTap(product),
                              onLongPress: () => _onProductLongPress(product),
                              isAdmin: isAdmin,
                            );
                          },
                        ),
                      ),
          ),
        ],
      ),
      bottomNavigationBar: CustomBottomBar(
        currentIndex:
            CustomBottomBar.getIndexForRoute('/commodity-store-screen'),
      ),
      floatingActionButton: _cartItemCount > 0
          ? FloatingActionButton.extended(
              onPressed: _viewCart,
              icon: CustomIconWidget(
                iconName: 'shopping_cart',
                color: theme.floatingActionButtonTheme.foregroundColor,
                size: 20,
              ),
              label: Text(
                'Cart ($_cartItemCount)',
                style: GoogleFonts.inter(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            )
          : null,
    );
  }
}
