import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:google_fonts/google_fonts.dart'; // Add this import

import '../../../core/app_export.dart';
import '../../../widgets/custom_icon_widget.dart';

class EmptyStateWidget extends StatelessWidget {
  final String title;
  final String subtitle;
  final String iconName;
  final List<String> suggestions;
  final List<String> categoryShortcuts;
  final ValueChanged<String>? onSuggestionTap;
  final ValueChanged<String>? onCategoryTap;

  const EmptyStateWidget({
    super.key,
    required this.title,
    required this.subtitle,
    required this.iconName,
    this.suggestions = const [],
    this.categoryShortcuts = const [],
    this.onSuggestionTap,
    this.onCategoryTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Empty State Icon
            Container(
              width: 80.w,
              height: 20.h,
              decoration: BoxDecoration(
                color: colorScheme.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: CustomIconWidget(
                  iconName: iconName,
                  color: colorScheme.primary.withValues(alpha: 0.6),
                  size: 48,
                ),
              ),
            ),
            SizedBox(height: 24.0),
            // Title
            Text(
              title,
              style: GoogleFonts.inter(
                fontSize: 20.sp,
                fontWeight: FontWeight.w600,
                color: colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.0),
            // Subtitle
            Text(
              subtitle,
              style: GoogleFonts.inter(
                fontSize: 14.sp,
                color: colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 32.0),
            // Suggestions
            if (suggestions.isNotEmpty) ...[
              Text(
                'Try searching for:',
                style: GoogleFonts.inter(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: colorScheme.onSurface.withValues(alpha: 0.8),
                ),
              ),
              SizedBox(height: 12.0),
              Wrap(
                spacing: 8.0,
                runSpacing: 8.0,
                children: suggestions
                    .map(
                      (suggestion) => GestureDetector(
                        onTap: () => onSuggestionTap?.call(suggestion),
                        child: Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 16.0, vertical: 8.0),
                          decoration: BoxDecoration(
                            color: colorScheme.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(20.0),
                            border: Border.all(
                              color: colorScheme.primary.withValues(alpha: 0.3),
                            ),
                          ),
                          child: Text(
                            suggestion,
                            style: GoogleFonts.inter(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w500,
                              color: colorScheme.primary,
                            ),
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
              SizedBox(height: 24.0),
            ],
            // Category Shortcuts
            if (categoryShortcuts.isNotEmpty) ...[
              Text(
                'Browse by category:',
                style: GoogleFonts.inter(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: colorScheme.onSurface.withValues(alpha: 0.8),
                ),
              ),
              SizedBox(height: 12.0),
              Column(
                children: categoryShortcuts
                    .map(
                      (category) => Container(
                        width: double.infinity,
                        margin: EdgeInsets.only(bottom: 8.0),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () => onCategoryTap?.call(category),
                            borderRadius: BorderRadius.circular(12.0),
                            child: Container(
                              padding: EdgeInsets.all(16.0),
                              decoration: BoxDecoration(
                                color: colorScheme.surface,
                                borderRadius: BorderRadius.circular(12.0),
                                border: Border.all(
                                  color: colorScheme.outline
                                      .withValues(alpha: 0.2),
                                ),
                              ),
                              child: Row(
                                children: [
                                  CustomIconWidget(
                                    iconName: _getCategoryIcon(category),
                                    color: colorScheme.primary,
                                    size: 24,
                                  ),
                                  SizedBox(width: 12.0),
                                  Expanded(
                                    child: Text(
                                      category,
                                      style: GoogleFonts.inter(
                                        fontSize: 14.sp,
                                        fontWeight: FontWeight.w500,
                                        color: colorScheme.onSurface,
                                      ),
                                    ),
                                  ),
                                  CustomIconWidget(
                                    iconName: 'arrow_forward_ios',
                                    color: colorScheme.onSurface
                                        .withValues(alpha: 0.4),
                                    size: 16,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'grains':
        return 'grain';
      case 'tools':
        return 'build';
      case 'fertilizers':
        return 'eco';
      case 'seeds':
        return 'spa';
      default:
        return 'category';
    }
  }
}