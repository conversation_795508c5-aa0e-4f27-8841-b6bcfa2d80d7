import '../../../core/utils/currency_utils.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:google_fonts/google_fonts.dart'; // Add this import for GoogleFonts

import '../../../core/app_export.dart';

class QuickActionsBottomSheetWidget extends StatelessWidget {
  final Map<String, dynamic> product;
  final VoidCallback onAddToCart;
  final VoidCallback onViewDetails;
  final VoidCallback onShare;
  final VoidCallback onSubmitForApproval;

  const QuickActionsBottomSheetWidget({
    super.key,
    required this.product,
    required this.onAddToCart,
    required this.onViewDetails,
    required this.onShare,
    required this.onSubmitForApproval,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final stockLevel = (product['stock'] as int?) ?? 0;
    final isOutOfStock = stockLevel == 0;

    return Container(
      padding: EdgeInsets.all(20.0),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.0)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40.0,
            height: 4.0,
            decoration: BoxDecoration(
              color: colorScheme.onSurface.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2.0),
            ),
          ),
          SizedBox(height: 16.0),
          // Product Info
          Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8.0),
                child: CustomImageWidget(
                  imageUrl: (product['image'] as String?) ?? '',
                  width: 60,
                  height: 60,
                  fit: BoxFit.cover,
                ),
              ),
              SizedBox(width: 12.0),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      (product['name'] as String?) ?? 'Unknown Product',
                      style: GoogleFonts.inter(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: colorScheme.onSurface,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 4.0),
                    Text(
                      formatNaira(((product['price'] as num?) ?? 0).toDouble()),
                      style: GoogleFonts.inter(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: colorScheme.primary,
                      ),
                    ),
                    if (!isOutOfStock)
                      Text(
                        '$stockLevel in stock',
                        style: GoogleFonts.inter(
                          fontSize: 12.sp,
                          color: colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 24.0),
          // Action Buttons
          Column(
            children: [
              _buildActionButton(
                context,
                icon: 'add_shopping_cart',
                title: 'Add to Cart',
                subtitle:
                    isOutOfStock ? 'Out of stock' : 'Add item to your cart',
                onTap: isOutOfStock ? null : onAddToCart,
                isPrimary: true,
                isEnabled: !isOutOfStock,
              ),
              SizedBox(height: 12.0),
              _buildActionButton(
                context,
                icon: 'check_circle',
                title: 'Submit for Approval',
                subtitle: 'Send request to admin for approval',
                onTap: isOutOfStock ? null : onSubmitForApproval,
                isPrimary: false,
                isEnabled: !isOutOfStock,
              ),
              SizedBox(height: 12.0),
              _buildActionButton(
                context,
                icon: 'visibility',
                title: 'View Details',
                subtitle: 'See full product information',
                onTap: onViewDetails,
              ),
              SizedBox(height: 12.0),
              _buildActionButton(
                context,
                icon: 'share',
                title: 'Share Product',
                subtitle: 'Share with friends and family',
                onTap: onShare,
              ),
            ],
          ),
          SizedBox(height: 16.0),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required String icon,
    required String title,
    required String subtitle,
    required VoidCallback? onTap,
    bool isPrimary = false,
    bool isEnabled = true,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: isEnabled ? onTap : null,
        borderRadius: BorderRadius.circular(12.0),
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: isPrimary && isEnabled
                ? colorScheme.primary.withValues(alpha: 0.1)
                : colorScheme.surface,
            borderRadius: BorderRadius.circular(12.0),
            border: Border.all(
              color: isPrimary && isEnabled
                  ? colorScheme.primary.withValues(alpha: 0.3)
                  : colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  color: isPrimary && isEnabled
                      ? colorScheme.primary
                      : colorScheme.onSurface
                          .withValues(alpha: isEnabled ? 0.1 : 0.05),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: CustomIconWidget(
                  iconName: icon,
                  color: isPrimary && isEnabled
                      ? colorScheme.onPrimary
                      : colorScheme.onSurface
                          .withValues(alpha: isEnabled ? 0.7 : 0.4),
                  size: 20,
                ),
              ),
              SizedBox(width: 12.0),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.inter(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: colorScheme.onSurface
                            .withValues(alpha: isEnabled ? 1.0 : 0.5),
                      ),
                    ),
                    SizedBox(height: 2.0),
                    Text(
                      subtitle,
                      style: GoogleFonts.inter(
                        fontSize: 12.sp,
                        color: colorScheme.onSurface
                            .withValues(alpha: isEnabled ? 0.6 : 0.4),
                      ),
                    ),
                  ],
                ),
              ),
              if (isEnabled)
                CustomIconWidget(
                  iconName: 'arrow_forward_ios',
                  color: colorScheme.onSurface.withValues(alpha: 0.4),
                  size: 16,
                ),
            ],
          ),
        ),
      ),
    );
  }

  static void show(
    BuildContext context, {
    required Map<String, dynamic> product,
    required VoidCallback onAddToCart,
    required VoidCallback onViewDetails,
    required VoidCallback onShare,
    required VoidCallback onSubmitForApproval,
  }) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.0)),
      ),
      builder: (context) => QuickActionsBottomSheetWidget(
        product: product,
        onAddToCart: onAddToCart,
        onViewDetails: onViewDetails,
        onShare: onShare,
        onSubmitForApproval: onSubmitForApproval,
      ),
    );
  }
}