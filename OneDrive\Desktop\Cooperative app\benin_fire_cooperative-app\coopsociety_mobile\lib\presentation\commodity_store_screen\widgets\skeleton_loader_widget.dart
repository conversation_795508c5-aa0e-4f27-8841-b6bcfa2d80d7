import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

class SkeletonLoaderWidget extends StatefulWidget {
  final int itemCount;

  const SkeletonLoaderWidget({
    super.key,
    this.itemCount = 6,
  });

  @override
  State<SkeletonLoaderWidget> createState() => _SkeletonLoaderWidgetState();
}

class _SkeletonLoaderWidgetState extends State<SkeletonLoaderWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return GridView.builder(
      padding: EdgeInsets.all(16.0),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 12.0,
        mainAxisSpacing: 12.0,
      ),
      itemCount: widget.itemCount,
      itemBuilder: (context, index) {
        return AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return Container(
              decoration: BoxDecoration(
                color: colorScheme.surface,
                borderRadius: BorderRadius.circular(12.0),
                boxShadow: [
                  BoxShadow(
                    color: theme.shadowColor.withValues(alpha: 0.05),
                    blurRadius: 4.0,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Image skeleton
                  Expanded(
                    flex: 3,
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: colorScheme.onSurface
                            .withValues(alpha: _animation.value * 0.1),
                        borderRadius:
                            BorderRadius.vertical(top: Radius.circular(12.0)),
                      ),
                    ),
                  ),
                  // Content skeleton
                  Expanded(
                    flex: 2,
                    child: Padding(
                      padding: EdgeInsets.all(12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Title skeleton
                          Container(
                            width: double.infinity,
                            height: 16.0,
                            decoration: BoxDecoration(
                              color: colorScheme.onSurface
                                  .withValues(alpha: _animation.value * 0.1),
                              borderRadius: BorderRadius.circular(4.0),
                            ),
                          ),
                          SizedBox(height: 8.0),
                          // Subtitle skeleton
                          Container(
                            width: 60.w,
                            height: 12.0,
                            decoration: BoxDecoration(
                              color: colorScheme.onSurface
                                  .withValues(alpha: _animation.value * 0.08),
                              borderRadius: BorderRadius.circular(4.0),
                            ),
                          ),
                          Spacer(),
                          // Price skeleton
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Container(
                                width: 20.w,
                                height: 16.0,
                                decoration: BoxDecoration(
                                  color: colorScheme.onSurface.withValues(
                                      alpha: _animation.value * 0.1),
                                  borderRadius: BorderRadius.circular(4.0),
                                ),
                              ),
                              Container(
                                width: 15.w,
                                height: 12.0,
                                decoration: BoxDecoration(
                                  color: colorScheme.onSurface.withValues(
                                      alpha: _animation.value * 0.08),
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
