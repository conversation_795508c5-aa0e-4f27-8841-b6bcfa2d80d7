import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../core/app_export.dart';
import '../../../core/types.dart';
import '../../../widgets/custom_icon_widget.dart';

class SortBottomSheetWidget extends StatelessWidget {
  final SortOption currentSort;
  final ValueChanged<SortOption> onSortChanged;

  const SortBottomSheetWidget({
    super.key,
    required this.currentSort,
    required this.onSortChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: EdgeInsets.all(20.0),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.0)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Sort Products',
                style: GoogleFonts.inter(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: colorScheme.onSurface,
                ),
              ),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: CustomIconWidget(
                  iconName: 'close',
                  color: colorScheme.onSurface.withValues(alpha: 0.6),
                  size: 24,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.0),
          // Sort Options
          _buildSortOption(
            context,
            'Price: Low to High',
            SortOption.priceAsc,
            Icons.arrow_upward,
          ),
          _buildSortOption(
            context,
            'Price: High to Low',
            SortOption.priceDesc,
            Icons.arrow_downward,
          ),
          _buildSortOption(
            context,
            'Name: A to Z',
            SortOption.nameAsc,
            Icons.sort_by_alpha,
          ),
          _buildSortOption(
            context,
            'Name: Z to A',
            SortOption.nameDesc,
            Icons.sort_by_alpha,
          ),
          _buildSortOption(
            context,
            'Stock: Low to High',
            SortOption.stockAsc,
            Icons.inventory,
          ),
          _buildSortOption(
            context,
            'Stock: High to Low',
            SortOption.stockDesc,
            Icons.inventory_2,
          ),
          _buildSortOption(
            context,
            'Newest First',
            SortOption.newest,
            Icons.new_releases,
          ),
          _buildSortOption(
            context,
            'Oldest First',
            SortOption.oldest,
            Icons.access_time,
          ),
          SizedBox(height: 16.0),
        ],
      ),
    );
  }

  Widget _buildSortOption(
    BuildContext context,
    String title,
    SortOption option,
    IconData icon,
  ) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isSelected = currentSort == option;

    return ListTile(
      contentPadding: EdgeInsets.symmetric(horizontal: 0, vertical: 4.0),
      leading: CustomIconWidget(
        iconName: icon.toString().split('.').last,
        color: isSelected
            ? colorScheme.primary
            : colorScheme.onSurface.withValues(alpha: 0.6),
        size: 24,
      ),
      title: Text(
        title,
        style: GoogleFonts.inter(
          fontSize: 14.sp,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          color: isSelected ? colorScheme.primary : colorScheme.onSurface,
        ),
      ),
      trailing: isSelected
          ? CustomIconWidget(
              iconName: 'check',
              color: colorScheme.primary,
              size: 20,
            )
          : null,
      onTap: () {
        onSortChanged(option);
        Navigator.pop(context);
      },
    );
  }

  static void show(
    BuildContext context, {
    required SortOption currentSort,
    required ValueChanged<SortOption> onSortChanged,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SortBottomSheetWidget(
        currentSort: currentSort,
        onSortChanged: onSortChanged,
      ),
    );
  }
}