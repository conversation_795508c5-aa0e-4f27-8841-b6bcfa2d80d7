
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:shared_preferences.dart';
import 'dart:convert';

import '../../core/app_export.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/loan_calculator_widget.dart';
import './widgets/document_upload_widget.dart';

class LoanApplicationScreen extends StatefulWidget {
  const LoanApplicationScreen({super.key});

  @override
  State<LoanApplicationScreen> createState() => _LoanApplicationScreenState();
}

class _LoanApplicationScreenState extends State<LoanApplicationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _purposeController = TextEditingController();
  final _scrollController = ScrollController();
  
  // Form data
  double _loanAmount = 0.0;
  int _selectedDuration = 12;
  String _selectedPurpose = '';
  List<XFile> _uploadedDocuments = [];
  double _monthlyPayment = 0.0;

  // Form validation
  final Map<String, String?> _errors = {
    'amount': null,
    'duration': null,
    'purpose': null,
    'documents': null,
  };

  // UI state
  bool _isSubmitting = false;
  bool _isDraftSaved = false;

  // Loan limits
  static const double _minLoanAmount = 10000.0;
  static const double _maxLoanAmount = 1000000.0;
  static const double _interestRate = 8.5;
  
  @override
  void dispose() {
    _purposeController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _loadSavedDraft();
  }

  Future<void> _loadSavedDraft() async {
    // TODO: Implement loading from SharedPreferences
    setState(() {
      _isDraftSaved = false;
    });
  }

  Future<void> _saveDraft() async {
    if (_loanAmount > 0 || _selectedDuration > 0 || _selectedPurpose.isNotEmpty) {
      // TODO: Implement saving to SharedPreferences
      setState(() {
        _isDraftSaved = true;
      });
    }
  }

  void _onLoanCalculated(double amount, int duration, double monthlyPayment) {
    setState(() {
      _loanAmount = amount;
      _selectedDuration = duration;
      _monthlyPayment = monthlyPayment;
      _errors['amount'] = null;
      _errors['duration'] = null;
    });
  }

  bool _validateForm() {
    bool isValid = true;

    if (_loanAmount < _minLoanAmount) {
      _errors['amount'] = 'Minimum loan amount is ₦${_minLoanAmount.toStringAsFixed(2)}';
      isValid = false;
    } else if (_loanAmount > _maxLoanAmount) {
      _errors['amount'] = 'Maximum loan amount is ₦${_maxLoanAmount.toStringAsFixed(2)}';
      isValid = false;
    }

    if (_selectedDuration <= 0) {
      _errors['duration'] = 'Please select a loan duration';
      isValid = false;
    }

    if (_selectedPurpose.isEmpty) {
      _errors['purpose'] = 'Please specify loan purpose';
      isValid = false;
    }

    if (_uploadedDocuments.isEmpty) {
      _errors['documents'] = 'Please upload required documents';
      isValid = false;
    }

    setState(() {});
    return isValid;
  }

  Future<void> _submitLoanApplication() async {
    if (!_validateForm()) return;

    setState(() => _isSubmitting = true);

    try {
      // TODO: Replace with actual API call
      await Future.delayed(Duration(seconds: 2));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Loan application submitted successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to submit application. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }
  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();
  final _purposeController = TextEditingController();
  
  // Form data
  double _loanAmount = 0.0;
  int _selectedDuration = 12;
  String _selectedPurpose = '';
  List<XFile> _uploadedDocuments = [];
  double _monthlyPayment = 0.0;

  // Form validation
  final Map<String, String?> _errors = {
    'amount': null,
    'duration': null,
    'purpose': null,
    'documents': null,
  };

  // UI state
  bool _isSubmitting = false;
  bool _isDraftSaved = false;
  int _currentStep = 0;

  // Loan limits
  static const double _minLoanAmount = 10000.0;
  static const double _maxLoanAmount = 1000000.0;
  static const double _interestRate = 8.5;

  @override
  void initState() {
    super.initState();
    _loadSavedDraft();
  }

  @override
  void dispose() {
    _purposeController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadSavedDraft() async {
    // TODO: Implement loading from SharedPreferences
    setState(() {
      _isDraftSaved = false;
    });
  }

  Future<void> _saveDraft() async {
    if (_loanAmount > 0 || _selectedDuration > 0 || _selectedPurpose.isNotEmpty) {
      // TODO: Implement saving to SharedPreferences
      setState(() {
        _isDraftSaved = true;
      });
    }
  }

  void _onLoanCalculated(double amount, int duration, double monthlyPayment) {
    setState(() {
      _loanAmount = amount;
      _selectedDuration = duration;
      _monthlyPayment = monthlyPayment;
    });
  }

  bool _validateForm() {
    bool isValid = true;

    if (_loanAmount < _minLoanAmount) {
      _errors['amount'] = 'Minimum loan amount is ₦${_minLoanAmount.toStringAsFixed(2)}';
      isValid = false;
    } else if (_loanAmount > _maxLoanAmount) {
      _errors['amount'] = 'Maximum loan amount is ₦${_maxLoanAmount.toStringAsFixed(2)}';
      isValid = false;
    }

    if (_selectedDuration <= 0) {
      _errors['duration'] = 'Please select a loan duration';
      isValid = false;
    }

    if (_selectedPurpose.isEmpty) {
      _errors['purpose'] = 'Please specify loan purpose';
      isValid = false;
    }

    if (_uploadedDocuments.isEmpty) {
      _errors['documents'] = 'Please upload required documents';
      isValid = false;
    }

    setState(() {});
    return isValid;
  }

  Future<void> _submitLoanApplication() async {
    if (!_validateForm()) return;

    setState(() => _isSubmitting = true);

    try {
      // TODO: Replace with actual API call
      await Future.delayed(Duration(seconds: 2));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Loan application submitted successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to submit application. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  void _startAutoSave() {
    // Auto-save every 30 seconds
    Future.delayed(Duration(seconds: 30), () {
      if (mounted) {
        _saveDraft();
        _startAutoSave();
      }
    });
  }

  void _loadDraftData() {
    // Load draft data from local storage (simulated)
    // In real implementation, use SharedPreferences or secure storage
    setState(() {
      _isDraftSaved = false;
    });
  }

  void _saveDraft() {
    if (_loanAmount > 0 ||
        _selectedDuration > 0 ||
        _selectedPurpose.isNotEmpty) {
      // Save draft data (simulated)
      setState(() {
        _isDraftSaved = true;
        _lastAutoSave = DateTime.now();
      });
    }
  }

  void _validateForm() {
    setState(() {
      _errors.forEach((key, value) => _errors[key] = null);
      _currentStep = 1;
    });

    bool isValid = true;

    // Validate amount
    if (_loanAmount <= 0) {
      setState(() {
        _errors['amount'] = 'Please enter a valid loan amount';
        isValid = false;
      });
    } else if (_loanAmount < _minLoanAmount) {
      setState(() {
        _errors['amount'] = 'Minimum loan amount is ₦${_minLoanAmount.toStringAsFixed(2)}';
        isValid = false;
      });
    } else if (_loanAmount > _maxLoanAmount) {
      setState(() {
        _errors['amount'] = 'Maximum loan amount is ₦${_maxLoanAmount.toStringAsFixed(2)}';
        isValid = false;
      });
    }

    // Validate duration
    if (_selectedDuration <= 0) {
      setState(() {
        _errors['duration'] = 'Please select a loan duration';
        isValid = false;
      });
    }

    // Validate purpose
    if (_selectedPurpose.isEmpty) {
      setState(() {
        _errors['purpose'] = 'Please select or specify loan purpose';
        isValid = false;
      });
    }

    // Validate documents
    if (_uploadedDocuments.isEmpty) {
      setState(() {
        _errors['documents'] = 'Please upload required documents';
        isValid = false;
      });
    }

    if (isValid) {
      _submitLoanApplication();
    }
        _amountError = 'Please enter a valid loan amount';
        _currentStep = 1;
      });
      return;
    }

    if (_loanAmount < 1000) {
      setState(() {
        _amountError = 'Minimum loan amount is ₦1,000';
        _currentStep = 1;
      });
      return;
    }

    if (_loanAmount > 100000) {
      setState(() {
        _amountError = 'Maximum loan amount is ₦100,000';
        _currentStep = 1;
      });
      return;
    }

    setState(() => _currentStep = 2);

    // Validate duration
    if (_selectedDuration <= 0) {
      setState(() {
        _durationError = 'Please select a loan duration';
        _currentStep = 2;
      });
      return;
    }

    setState(() => _currentStep = 3);

    // Validate purpose
    if (_selectedPurpose.isEmpty) {
      setState(() {
        _purposeError = 'Please select or specify loan purpose';
        _currentStep = 3;
      });
      return;
    }

    setState(() => _currentStep = 4);

    // Validate documents (optional but recommended)
    if (_uploadedDocuments.isEmpty) {
      setState(() {
        _documentsError =
            'Uploading documents will help process your application faster';
      });
    }

    // If all validations pass
    if (_amountError == null &&
        _durationError == null &&
        _purposeError == null) {
      _submitApplication();
    }
  }

  Future<void> _submitApplication() async {
    setState(() {
      _isSubmitting = true;
    });

    try {
      // Simulate API call
      await Future.delayed(Duration(seconds: 2));

      // Generate application reference number
      final referenceNumber =
          'LA${DateTime.now().millisecondsSinceEpoch.toString().substring(7)}';

      // Clear draft data
      setState(() {
        _isDraftSaved = false;
      });

      // Show success dialog
      _showSuccessDialog(referenceNumber);
    } catch (e) {
      // Handle network error
      _showErrorDialog(
          'Failed to submit application. Please check your connection and try again.');
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  void _showSuccessDialog(String referenceNumber) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
        title: Row(
          children: [
            CustomIconWidget(
              iconName: 'check_circle',
              color: AppTheme.lightTheme.colorScheme.secondary,
              size: 24,
            ),
            SizedBox(width: 2.w),
            Text('Application Submitted'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Your loan application has been submitted successfully.',
              style: AppTheme.lightTheme.textTheme.bodyMedium,
            ),
            SizedBox(height: 2.h),
            Container(
              padding: EdgeInsets.all(3.w),
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.colorScheme.primary
                    .withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Reference Number:',
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.primary,
                    ),
                  ),
                  Text(
                    referenceNumber,
                    style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.lightTheme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 2.h),
            Text(
              'You can track your application status in the Loans section.',
              style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                color: AppTheme.lightTheme.colorScheme.onSurface
                    .withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.pushNamedAndRemoveUntil(
                context,
                '/loan-balance-sheet-screen',
                (route) => route.isFirst,
              );
            },
            child: Text('View Loans'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.pushNamedAndRemoveUntil(
                context,
                '/member-dashboard',
                (route) => route.isFirst,
              );
            },
            child: Text('Go to Dashboard'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
        title: Row(
          children: [
            CustomIconWidget(
              iconName: 'error_outline',
              color: AppTheme.lightTheme.colorScheme.error,
              size: 24,
            ),
            SizedBox(width: 2.w),
            Text('Submission Failed'),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showLoanPolicies() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: EdgeInsets.all(4.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                  width: 12.w,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppTheme.lightTheme.colorScheme.outline,
                    borderRadius: BorderRadius.circular(2.0),
                  ),
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                'Loan Policies & Eligibility',
                style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 2.h),
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildPolicySection(
                        'Eligibility Requirements',
                        [
                          'Must be an active BAFSC member for at least 6 months',
                          'Age between 18-65 years',
                          'Regular income source verification required',
                          'No outstanding loan defaults',
                        ],
                      ),
                      _buildPolicySection(
                        'Loan Terms',
                        [
                          'Interest rate: 12% per annum (reducing balance)',
                          'Processing fee: 2% of loan amount',
                          'Minimum amount: ₦1,000',
                          'Maximum amount: ₦100,000',
                          'Repayment period: 6-60 months',
                        ],
                      ),
                      _buildPolicySection(
                        'Required Documents',
                        [
                          'Valid government-issued ID',
                          'Proof of income (salary slips/bank statements)',
                          'BAFSC membership certificate',
                          'Collateral documents (for loans above ₦50,000)',
                        ],
                      ),
                      _buildPolicySection(
                        'Processing Timeline',
                        [
                          'Application review: 2-3 business days',
                          'Document verification: 1-2 business days',
                          'Approval decision: 1 business day',
                          'Fund disbursement: Same day after approval',
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPolicySection(String title, List<String> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.lightTheme.colorScheme.primary,
          ),
        ),
        SizedBox(height: 1.h),
        ...items.map((item) => Padding(
              padding: EdgeInsets.only(bottom: 0.5.h),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomIconWidget(
                    iconName: 'fiber_manual_record',
                    color: AppTheme.lightTheme.colorScheme.primary,
                    size: 8,
                  ),
                  SizedBox(width: 2.w),
                  Expanded(
                    child: Text(
                      item,
                      style: AppTheme.lightTheme.textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
            )),
        SizedBox(height: 2.h),
      ],
    );
  }

  bool get _isFormValid {
    return _loanAmount > 0 &&
        _loanAmount >= 1000 &&
        _loanAmount <= 100000 &&
        _selectedDuration > 0 &&
        _selectedPurpose.isNotEmpty;
  }

  Future<void> _submitLoanApplication() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isSubmitting = true);

    try {
      // TODO: Implement actual API call
      await Future.delayed(Duration(seconds: 2)); // Simulate API call

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Loan application submitted successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to submit application. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Loan Application',
        variant: CustomAppBarVariant.minimal,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          controller: _scrollController,
          padding: EdgeInsets.all(4.w),
          child: Column(
            children: [
              Card(
                child: Padding(
                  padding: EdgeInsets.all(4.w),
                  child: LoanCalculator(
                    initialAmount: _loanAmount,
                    initialDuration: _selectedDuration,
                    interestRate: _interestRate,
                    onCalculate: _onLoanCalculated,
                  ),
                ),
              ),
              SizedBox(height: 3.h),
              Card(
                child: Padding(
                  padding: EdgeInsets.all(4.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Loan Purpose',
                        style: theme.textTheme.titleLarge,
                      ),
                      SizedBox(height: 2.h),
                      TextFormField(
                        controller: _purposeController,
                        decoration: InputDecoration(
                          labelText: 'Purpose of Loan',
                          border: OutlineInputBorder(),
                          errorText: _errors['purpose'],
                        ),
                        maxLines: 3,
                        onChanged: (value) => setState(() {
                          _selectedPurpose = value;
                          _errors['purpose'] = null;
                        }),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 3.h),
              Card(
                child: Padding(
                  padding: EdgeInsets.all(4.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Required Documents',
                        style: theme.textTheme.titleLarge,
                      ),
                      SizedBox(height: 2.h),
                      if (_errors['documents'] != null)
                        Padding(
                          padding: EdgeInsets.only(bottom: 2.h),
                          child: Text(
                            _errors['documents']!,
                            style: TextStyle(
                              color: theme.colorScheme.error,
                              fontSize: 12.sp,
                            ),
                          ),
                        ),
                      DocumentUploadWidget(
                        onDocumentsSelected: (docs) {
                          setState(() {
                            _uploadedDocuments = docs;
                            _errors['documents'] = null;
                          });
                        },
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 3.h),
              SizedBox(
                width: double.infinity,
                height: 6.h,
                child: ElevatedButton(
                  onPressed: _isSubmitting ? null : _submitLoanApplication,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                  ),
                  child: _isSubmitting
                      ? SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              theme.colorScheme.onPrimary,
                            ),
                          ),
                        )
                      : Text(
                          'Submit Application',
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Loan Application',
          style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
            color: AppTheme.lightTheme.colorScheme.onPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppTheme.lightTheme.colorScheme.primary,
        foregroundColor: AppTheme.lightTheme.colorScheme.onPrimary,
        elevation: 1.0,
        leading: IconButton(
          icon: CustomIconWidget(
            iconName: 'arrow_back_ios',
            color: AppTheme.lightTheme.colorScheme.onPrimary,
            size: 20,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: CustomIconWidget(
              iconName: 'help_outline',
              color: AppTheme.lightTheme.colorScheme.onPrimary,
              size: 24,
            ),
            onPressed: _showLoanPolicies,
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: Stepper(
          type: StepperType.vertical,
          currentStep: _currentStep,
          onStepContinue: () {
            if (_currentStep < 3) {
              setState(() => _currentStep += 1);
            } else {
              if (_isFormValid && !_isSubmitting) _validateForm();
            }
          },
          onStepCancel: () {
            if (_currentStep > 0) setState(() => _currentStep -= 1);
          },
          controlsBuilder: (context, details) {
            return Row(
              children: [
                ElevatedButton(
                  onPressed: details.onStepContinue,
                  child: Text(_currentStep == 3 ? 'Submit' : 'Next'),
                ),
                if (_currentStep > 0)
                  TextButton(
                    onPressed: details.onStepCancel,
                    child: Text('Back'),
                  ),
              ],
            );
          },
          steps: [
            Step(
              title: Text('Amount'),
              isActive: _currentStep >= 0,
              state: _currentStep > 0 ? StepState.complete : StepState.indexed,
              content: LoanAmountInputWidget(
                controller: _amountController,
                onChanged: (value) {
                  setState(() {
                    _loanAmount = double.tryParse(value) ?? 0.0;
                    _amountError = null;
                  });
                },
                errorText: _amountError,
                minAmount: 1000.0,
                maxAmount: 100000.0,
              ),
            ),
            Step(
              title: Text('Duration'),
              isActive: _currentStep >= 1,
              state: _currentStep > 1 ? StepState.complete : StepState.indexed,
              content: LoanDurationSelectorWidget(
                selectedDuration: _selectedDuration,
                onChanged: (duration) {
                  setState(() {
                    _selectedDuration = duration;
                    _durationError = null;
                  });
                },
                errorText: _durationError,
              ),
            ),
            Step(
              title: Text('Purpose'),
              isActive: _currentStep >= 2,
              state: _currentStep > 2 ? StepState.complete : StepState.indexed,
              content: LoanPurposeSelectorWidget(
                selectedPurpose: _selectedPurpose,
                onChanged: (purpose) {
                  setState(() {
                    _selectedPurpose = purpose;
                    _purposeError = null;
                  });
                },
                errorText: _purposeError,
              ),
            ),
            Step(
              title: Text('Documents'),
              isActive: _currentStep >= 3,
              state: StepState.indexed,
              content: DocumentUploadWidget(
                uploadedFiles: _uploadedDocuments,
                onFilesChanged: (files) {
                  setState(() {
                    _uploadedDocuments = files;
                    _documentsError = null;
                  });
                },
                errorText: _documentsError,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
