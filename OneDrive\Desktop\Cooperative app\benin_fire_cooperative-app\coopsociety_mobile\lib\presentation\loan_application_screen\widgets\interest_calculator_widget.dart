import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'dart:math' as math;

import '../../../core/app_export.dart';

class InterestCalculatorWidget extends StatelessWidget {
  final double loanAmount;
  final int duration;
  final double interestRate;

  const InterestCalculatorWidget({
    super.key,
    required this.loanAmount,
    required this.duration,
    this.interestRate = 12.0, // Default 12% annual interest
  });

  double get monthlyPayment {
    if (loanAmount <= 0 || duration <= 0) return 0.0;

    final monthlyRate = interestRate / 100 / 12;
    final totalPayments = duration.toDouble();

    if (monthlyRate == 0) {
      return loanAmount / totalPayments;
    }

    final payment = loanAmount *
        (monthlyRate * math.pow(1 + monthlyRate, totalPayments)) /
        (math.pow(1 + monthlyRate, totalPayments) - 1);

    return payment;
  }

  double get totalAmount => monthlyPayment * duration;
  double get totalInterest => totalAmount - loanAmount;

  @override
  Widget build(BuildContext context) {
    if (loanAmount <= 0 || duration <= 0) {
      return SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(
          color: AppTheme.lightTheme.colorScheme.primary.withValues(alpha: 0.2),
          width: 1.0,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CustomIconWidget(
                iconName: 'calculate',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 20,
              ),
              SizedBox(width: 2.w),
              Text(
                'Loan Calculator',
                style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.lightTheme.colorScheme.primary,
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),

          // Monthly Payment
          _buildCalculationRow(
            'Monthly Payment',
            '\$${monthlyPayment.toStringAsFixed(2)}',
            Icons.payments_outlined,
          ),
          SizedBox(height: 1.h),

          // Total Interest
          _buildCalculationRow(
            'Total Interest',
            '\$${totalInterest.toStringAsFixed(2)}',
            Icons.trending_up_outlined,
          ),
          SizedBox(height: 1.h),

          // Total Amount
          _buildCalculationRow(
            'Total Amount',
            '\$${totalAmount.toStringAsFixed(2)}',
            Icons.account_balance_wallet_outlined,
            isHighlighted: true,
          ),
          SizedBox(height: 2.h),

          // Interest Rate Info
          Container(
            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.surface,
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'info_outline',
                  color: AppTheme.lightTheme.colorScheme.onSurface
                      .withValues(alpha: 0.7),
                  size: 16,
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    'Interest Rate: ${interestRate.toStringAsFixed(1)}% per annum',
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurface
                          .withValues(alpha: 0.7),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCalculationRow(String label, String value, IconData icon,
      {bool isHighlighted = false}) {
    return Row(
      children: [
        CustomIconWidget(
          iconName: icon.toString().split('.').last,
          color: isHighlighted
              ? AppTheme.lightTheme.colorScheme.primary
              : AppTheme.lightTheme.colorScheme.onSurface
                  .withValues(alpha: 0.7),
          size: 18,
        ),
        SizedBox(width: 3.w),
        Expanded(
          child: Text(
            label,
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: isHighlighted
                  ? AppTheme.lightTheme.colorScheme.primary
                  : AppTheme.lightTheme.colorScheme.onSurface,
              fontWeight: isHighlighted ? FontWeight.w600 : FontWeight.w400,
            ),
          ),
        ),
        Text(
          value,
          style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: isHighlighted
                ? AppTheme.lightTheme.colorScheme.primary
                : AppTheme.lightTheme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }
}

// Math utility class for calculations
class Math {
  static double pow(double base, double exponent) {
    return math.pow(base, exponent).toDouble();
  }
}