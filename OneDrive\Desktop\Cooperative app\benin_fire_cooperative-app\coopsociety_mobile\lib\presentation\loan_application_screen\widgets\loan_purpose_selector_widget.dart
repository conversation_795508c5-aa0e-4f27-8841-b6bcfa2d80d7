import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/app_theme.dart';

class LoanPurposeSelectorWidget extends StatefulWidget {
  final String selectedPurpose;
  final Function(String) onChanged;
  final String? errorText;

  const LoanPurposeSelectorWidget({
    super.key,
    required this.selectedPurpose,
    required this.onChanged,
    this.errorText,
  });

  @override
  State<LoanPurposeSelectorWidget> createState() =>
      _LoanPurposeSelectorWidgetState();
}

class _LoanPurposeSelectorWidgetState extends State<LoanPurposeSelectorWidget> {
  final List<String> _purposeOptions = [
    'Business Expansion',
    'Agriculture',
    'Education',
    'Medical Emergency',
    'Home Improvement',
    'Equipment Purchase',
    'Working Capital',
    'Other',
  ];

  final TextEditingController _otherController = TextEditingController();
  bool _showOtherInput = false;

  @override
  void initState() {
    super.initState();
    _showOtherInput = widget.selectedPurpose.isNotEmpty &&
        !_purposeOptions.contains(widget.selectedPurpose);
    if (_showOtherInput) {
      _otherController.text = widget.selectedPurpose;
    }
  }

  @override
  void dispose() {
    _otherController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Loan Purpose',
          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 1.h),
        Wrap(
          spacing: 2.w,
          runSpacing: 1.h,
          children: _purposeOptions.map((purpose) {
            final isSelected = purpose == 'Other'
                ? _showOtherInput
                : widget.selectedPurpose == purpose;

            return GestureDetector(
              onTap: () {
                if (purpose == 'Other') {
                  setState(() {
                    _showOtherInput = true;
                  });
                  widget.onChanged(_otherController.text);
                } else {
                  setState(() {
                    _showOtherInput = false;
                  });
                  widget.onChanged(purpose);
                }
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppTheme.lightTheme.colorScheme.primary
                      : AppTheme.lightTheme.colorScheme.surface,
                  border: Border.all(
                    color: isSelected
                        ? AppTheme.lightTheme.colorScheme.primary
                        : AppTheme.lightTheme.colorScheme.outline,
                    width: 1.0,
                  ),
                  borderRadius: BorderRadius.circular(20.0),
                ),
                child: Text(
                  purpose,
                  style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                    color: isSelected
                        ? AppTheme.lightTheme.colorScheme.onPrimary
                        : AppTheme.lightTheme.colorScheme.onSurface,
                    fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
        if (_showOtherInput) ...[
          SizedBox(height: 2.h),
          TextFormField(
            controller: _otherController,
            decoration: InputDecoration(
              hintText: 'Please specify your loan purpose',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
                borderSide: BorderSide(
                  color: AppTheme.lightTheme.colorScheme.outline,
                  width: 1.0,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
                borderSide: BorderSide(
                  color: AppTheme.lightTheme.colorScheme.primary,
                  width: 2.0,
                ),
              ),
              contentPadding:
                  EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
            ),
            style: AppTheme.lightTheme.textTheme.bodyLarge,
            maxLines: 2,
            onChanged: widget.onChanged,
          ),
        ],
        if (widget.errorText != null) ...[
          SizedBox(height: 0.5.h),
          Text(
            widget.errorText!,
            style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
              color: AppTheme.lightTheme.colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }
}
