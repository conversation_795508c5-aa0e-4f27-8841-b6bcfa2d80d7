import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/custom_bottom_bar.dart';
import '../../widgets/custom_icon_widget.dart';
import './widgets/filter_chips_widget.dart';
import './widgets/loan_card_widget.dart';
import './widgets/loan_summary_widget.dart';
import './widgets/payment_history_widget.dart';

class LoanBalanceSheetScreen extends StatefulWidget {
  const LoanBalanceSheetScreen({super.key});

  @override
  State<LoanBalanceSheetScreen> createState() => _LoanBalanceSheetScreenState();
}

class _LoanBalanceSheetScreenState extends State<LoanBalanceSheetScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _selectedFilter = 'All';
  bool _isRefreshing = false;

  final List<String> _filters = ['All', 'Active', 'Completed', 'Overdue'];

  // Mock data for loan summary
  final Map<String, dynamic> _summaryData = {
    'totalOutstanding': 45750.00,
    'nextPaymentAmount': 1250.00,
    'nextPaymentDate': 'Dec 15, 2024',
    'totalLoans': 3,
    'activeLoans': 2,
    'overdueAmount': 0.00,
  };

  // Mock data for loans
  final List<Map<String, dynamic>> _loansData = [
    {
      'id': 'LN001',
      'type': 'Personal Loan',
      'amount': 25000.00,
      'paidAmount': 15000.00,
      'interestRate': 8.5,
      'remainingTerm': 18,
      'status': 'active',
      'nextPaymentDate': 'Dec 15, 2024',
      'nextPaymentAmount': 750.00,
    },
    {
      'id': 'LN002',
      'type': 'Business Loan',
      'amount': 50000.00,
      'paidAmount': 30000.00,
      'interestRate': 12.0,
      'remainingTerm': 24,
      'status': 'active',
      'nextPaymentDate': 'Dec 20, 2024',
      'nextPaymentAmount': 1200.00,
    },
    {
      'id': 'LN003',
      'type': 'Emergency Loan',
      'amount': 5000.00,
      'paidAmount': 5000.00,
      'interestRate': 6.0,
      'remainingTerm': 0,
      'status': 'completed',
      'nextPaymentDate': '',
      'nextPaymentAmount': 0.00,
    },
  ];

  // Mock data for payment history
  final List<Map<String, dynamic>> _paymentHistory = [
    {
      'id': 'PAY001',
      'amount': 750.00,
      'date': 'Nov 15, 2024',
      'type': 'Monthly Payment',
      'status': 'completed',
      'method': 'Bank Transfer',
      'transactionId': 'TXN123456789',
      'loanId': 'LN001',
    },
    {
      'id': 'PAY002',
      'amount': 1200.00,
      'date': 'Nov 20, 2024',
      'type': 'Monthly Payment',
      'status': 'completed',
      'method': 'Credit Card',
      'transactionId': 'TXN987654321',
      'loanId': 'LN002',
    },
    {
      'id': 'PAY003',
      'amount': 500.00,
      'date': 'Oct 25, 2024',
      'type': 'Partial Payment',
      'status': 'completed',
      'method': 'Bank Transfer',
      'transactionId': 'TXN456789123',
      'loanId': 'LN003',
    },
    {
      'id': 'PAY004',
      'amount': 750.00,
      'date': 'Oct 15, 2024',
      'type': 'Monthly Payment',
      'status': 'completed',
      'method': 'Bank Transfer',
      'transactionId': 'TXN789123456',
      'loanId': 'LN001',
    },
    {
      'id': 'PAY005',
      'amount': 1200.00,
      'date': 'Oct 20, 2024',
      'type': 'Monthly Payment',
      'status': 'completed',
      'method': 'Credit Card',
      'transactionId': 'TXN321654987',
      'loanId': 'LN002',
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  List<Map<String, dynamic>> get _filteredLoans {
    switch (_selectedFilter) {
      case 'Active':
        return _loansData.where((loan) => loan['status'] == 'active').toList();
      case 'Completed':
        return _loansData
            .where((loan) => loan['status'] == 'completed')
            .toList();
      case 'Overdue':
        return _loansData.where((loan) => loan['status'] == 'overdue').toList();
      default:
        return _loansData;
    }
  }

  Future<void> _handleRefresh() async {
    setState(() {
      _isRefreshing = true;
    });

    // Simulate API call
    await Future.delayed(Duration(seconds: 2));

    setState(() {
      _isRefreshing = false;
    });

    // Provide haptic feedback
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Loan data updated successfully'),
          behavior: SnackBarBehavior.floating,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _handleMakePayment() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(4.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12.w,
              height: 0.5.h,
              decoration: BoxDecoration(
                color: Theme.of(context)
                    .colorScheme
                    .outline
                    .withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2.0),
              ),
            ),
            SizedBox(height: 2.h),
            Text(
              'Make Payment',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            SizedBox(height: 2.h),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'account_balance',
                color: Theme.of(context).colorScheme.primary,
                size: 24,
              ),
              title: Text('Bank Transfer'),
              subtitle: Text('Direct transfer from your bank account'),
              trailing: CustomIconWidget(
                iconName: 'arrow_forward_ios',
                color: Theme.of(context)
                    .colorScheme
                    .onSurface
                    .withValues(alpha: 0.6),
                size: 16,
              ),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Redirecting to bank transfer...')),
                );
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'credit_card',
                color: Theme.of(context).colorScheme.primary,
                size: 24,
              ),
              title: Text('Credit/Debit Card'),
              subtitle: Text('Pay using your saved cards'),
              trailing: CustomIconWidget(
                iconName: 'arrow_forward_ios',
                color: Theme.of(context)
                    .colorScheme
                    .onSurface
                    .withValues(alpha: 0.6),
                size: 16,
              ),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Opening card payment...')),
                );
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'account_balance_wallet',
                color: Theme.of(context).colorScheme.primary,
                size: 24,
              ),
              title: Text('Digital Wallet'),
              subtitle: Text('Pay using mobile wallet'),
              trailing: CustomIconWidget(
                iconName: 'arrow_forward_ios',
                color: Theme.of(context)
                    .colorScheme
                    .onSurface
                    .withValues(alpha: 0.6),
                size: 16,
              ),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Opening digital wallet...')),
                );
              },
            ),
            SizedBox(height: 2.h),
          ],
        ),
      ),
    );
  }

  void _handleViewReceipt() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Receipt downloaded to your device'),
        action: SnackBarAction(
          label: 'View',
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Opening receipt viewer...')),
            );
          },
        ),
      ),
    );
  }

  void _handleExportStatement() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Export Statement'),
        content: Text('Choose export format for your loan statement'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('PDF statement exported successfully')),
              );
            },
            child: Text('PDF'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('CSV statement exported successfully')),
              );
            },
            child: Text('CSV'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Loan Balance Sheet',
        variant: CustomAppBarVariant.primary,
        actions: [
          IconButton(
            onPressed: _handleExportStatement,
            icon: CustomIconWidget(
              iconName: 'file_download',
              color: colorScheme.onPrimary,
              size: 24,
            ),
            tooltip: 'Export Statement',
          ),
          PopupMenuButton<String>(
            icon: CustomIconWidget(
              iconName: 'more_vert',
              color: colorScheme.onPrimary,
              size: 24,
            ),
            onSelected: (value) {
              switch (value) {
                case 'apply':
                  Navigator.pushNamed(context, '/loan-application-screen');
                  break;
                case 'calculator':
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Opening loan calculator...')),
                  );
                  break;
                case 'support':
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Connecting to support...')),
                  );
                  break;
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'apply',
                child: Row(
                  children: [
                    CustomIconWidget(
                      iconName: 'add_circle_outline',
                      color: colorScheme.onSurface,
                      size: 20,
                    ),
                    SizedBox(width: 3.w),
                    Text('Apply for Loan'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'calculator',
                child: Row(
                  children: [
                    CustomIconWidget(
                      iconName: 'calculate',
                      color: colorScheme.onSurface,
                      size: 20,
                    ),
                    SizedBox(width: 3.w),
                    Text('Loan Calculator'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'support',
                child: Row(
                  children: [
                    CustomIconWidget(
                      iconName: 'support_agent',
                      color: colorScheme.onSurface,
                      size: 20,
                    ),
                    SizedBox(width: 3.w),
                    Text('Contact Support'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          Container(
            color: colorScheme.primary,
            child: TabBar(
              controller: _tabController,
              tabs: [
                Tab(
                  icon: CustomIconWidget(
                    iconName: 'account_balance_wallet',
                    color: colorScheme.onPrimary,
                    size: 20,
                  ),
                  text: 'Loans',
                ),
                Tab(
                  icon: CustomIconWidget(
                    iconName: 'history',
                    color: colorScheme.onPrimary,
                    size: 20,
                  ),
                  text: 'History',
                ),
              ],
              labelColor: colorScheme.onPrimary,
              unselectedLabelColor:
                  colorScheme.onPrimary.withValues(alpha: 0.7),
              indicatorColor: colorScheme.onPrimary,
            ),
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Loans Tab
                RefreshIndicator(
                  onRefresh: _handleRefresh,
                  child: CustomScrollView(
                    slivers: [
                      SliverToBoxAdapter(
                        child: LoanSummaryWidget(
                          summaryData: _summaryData,
                          onMakePayment: _handleMakePayment,
                        ),
                      ),
                      SliverToBoxAdapter(
                        child: SizedBox(height: 1.h),
                      ),
                      SliverToBoxAdapter(
                        child: FilterChipsWidget(
                          filters: _filters,
                          selectedFilter: _selectedFilter,
                          onFilterChanged: (filter) {
                            setState(() {
                              _selectedFilter = filter;
                            });
                          },
                        ),
                      ),
                      SliverToBoxAdapter(
                        child: SizedBox(height: 2.h),
                      ),
                      if (_filteredLoans.isEmpty) ...[
                        SliverFillRemaining(
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CustomIconWidget(
                                  iconName: _selectedFilter == 'All'
                                      ? 'account_balance_wallet'
                                      : 'filter_list_off',
                                  color: colorScheme.onSurface
                                      .withValues(alpha: 0.4),
                                  size: 64,
                                ),
                                SizedBox(height: 2.h),
                                Text(
                                  _selectedFilter == 'All'
                                      ? 'No loans found'
                                      : 'No $_selectedFilter loans',
                                  style: theme.textTheme.titleLarge?.copyWith(
                                    color: colorScheme.onSurface
                                        .withValues(alpha: 0.6),
                                  ),
                                ),
                                SizedBox(height: 1.h),
                                Text(
                                  _selectedFilter == 'All'
                                      ? 'Apply for your first loan to get started'
                                      : 'Try selecting a different filter',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    color: colorScheme.onSurface
                                        .withValues(alpha: 0.4),
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                if (_selectedFilter == 'All') ...[
                                  SizedBox(height: 3.h),
                                  ElevatedButton.icon(
                                    onPressed: () {
                                      Navigator.pushNamed(
                                          context, '/loan-application-screen');
                                    },
                                    icon: CustomIconWidget(
                                      iconName: 'add',
                                      color: colorScheme.onPrimary,
                                      size: 20,
                                    ),
                                    label: Text('Apply for Loan'),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                      ] else ...[
                        SliverList(
                          delegate: SliverChildBuilderDelegate(
                            (context, index) {
                              final loan = _filteredLoans[index];
                              return LoanCardWidget(
                                loanData: loan,
                                onMakePayment: _handleMakePayment,
                                onViewReceipt: _handleViewReceipt,
                              );
                            },
                            childCount: _filteredLoans.length,
                          ),
                        ),
                        SliverToBoxAdapter(
                          child: SizedBox(height: 10.h),
                        ),
                      ],
                    ],
                  ),
                ),
                // Payment History Tab
                PaymentHistoryWidget(
                  paymentHistory: _paymentHistory,
                  onViewReceipt: _handleViewReceipt,
                ),
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: CustomBottomBar(
        currentIndex:
            CustomBottomBar.getIndexForRoute('/loan-balance-sheet-screen'),
        onTap: (index) {
          // Navigation handled by CustomBottomBar
        },
      ),
      floatingActionButton:
          _tabController.index == 0 && _filteredLoans.isNotEmpty
              ? FloatingActionButton.extended(
                  onPressed: _handleMakePayment,
                  icon: CustomIconWidget(
                    iconName: 'payment',
                    color: Theme.of(context)
                            .floatingActionButtonTheme
                            .foregroundColor ??
                        Colors.black,
                    size: 20,
                  ),
                  label: Text('Make Payment'),
                )
              : null,
    );
  }
}
