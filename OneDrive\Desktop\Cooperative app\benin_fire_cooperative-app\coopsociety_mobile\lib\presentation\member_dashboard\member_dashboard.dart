import '../../widgets/aurora_background.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../widgets/custom_bottom_bar.dart';
import './widgets/activity_item_widget.dart';
import '../../core/utils/currency_utils.dart';
import './widgets/quick_actions_bottom_sheet.dart';
import './widgets/quick_stats_widget.dart';
import './widgets/welcome_header_widget.dart';

class MemberDashboard extends StatefulWidget {
  const MemberDashboard({super.key});

  @override
  State<MemberDashboard> createState() => _MemberDashboardState();
}

class _MemberDashboardState extends State<MemberDashboard> {
  // Mock approval requests for demonstration
  final List<Map<String, dynamic>> approvalRequests = [
    {
      "id": 1,
      "item": "Bag of Rice",
      "date": "2025-09-01",
      "status": "Pending",
    },
    {
      "id": 2,
      "item": "Cow Meat",
      "date": "2025-09-02",
      "status": "Approved",
    },
    {
      "id": 3,
      "item": "Indomie Noddles in Cartons",
      "date": "2025-09-03",
      "status": "Rejected",
    },
  ];
  Widget _buildApprovalRequestsSection() {
    return Card(
      margin: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Commodity Approval Requests',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12),
            ...approvalRequests.map((req) => ListTile(
                  leading: Icon(Icons.shopping_bag),
                  title: Text(req['item']),
                  subtitle: Text('Requested: ' + req['date']),
                  trailing: Text(
                    req['status'],
                    style: TextStyle(
                      color: req['status'] == 'Approved'
                          ? Colors.green
                          : req['status'] == 'Rejected'
                              ? Colors.red
                              : Colors.orange,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                )),
          ],
        ),
      ),
    );
  }
  bool _isRefreshing = false;
  final ScrollController _scrollController = ScrollController();

  // Mock member data
  final Map<String, dynamic> memberData = {
    "id": 1,
    "name": "Sarah Johnson",
  "membershipNumber": "BAFSC2024001",
    "joinDate": "2024-01-15",
    "status": "Active",
    "hasNotifications": true,
    "lastUpdated": "2025-08-30 18:35:00",
  };

  // Mock financial data
  final Map<String, dynamic> financialData = {
    "loanBalance": {
      "amount": 12450.00,
      "subtitle": "Next payment due: Sep 15, 2025",
      "outstandingAmount": 12450.00,
    },
    "savingsBalance": {
      "amount": 8750.25,
      "subtitle": "Interest earned this month: ₦45.30",
      "totalSavings": 8750.25,
    },
    "commoditiesAcquired": {
      "amount": "15 Items",
      "subtitle": "Total value: ₦3,200.00",
      "totalItems": 15,
    },
  };

  // Mock recent activities
  final List<Map<String, dynamic>> recentActivities = [
    {
      "id": 1,
      "title": "Loan Payment",
      "description": "Monthly installment payment processed",
      "amount": -450.00,
      "timestamp": "2 hours ago",
      "type": "loan_payment",
    },
    {
      "id": 2,
      "title": "Savings Deposit",
      "description": "Monthly savings contribution",
      "amount": 500.00,
      "timestamp": "1 day ago",
      "type": "savings_deposit",
    },
    {
      "id": 3,
      "title": "Commodity Purchase",
      "description": "Organic fertilizer - 50kg bag",
      "amount": -125.00,
      "timestamp": "3 days ago",
      "type": "commodity_purchase",
    },
    {
      "id": 4,
      "title": "Interest Credit",
      "description": "Monthly interest on savings",
      "amount": 45.30,
      "timestamp": "1 week ago",
      "type": "interest_credit",
    },
    {
      "id": 5,
      "title": "Loan Approval",
      "description": "Agricultural equipment loan approved",
      "amount": 5000.00,
      "timestamp": "2 weeks ago",
      "type": "loan_approval",
    },
  ];

  // Mock monthly stats
  final List<Map<String, dynamic>> monthlyStats = [
    {
      "title": "Loan Repayment Progress",
      "value": "68%",
      "progress": 0.68,
      "subtitle": "On track for early completion",
    },
    {
      "title": "Savings Goal",
      "value": "₦8,750 / ₦10,000",
      "progress": 0.875,
      "subtitle": "87% of annual target achieved",
    },
    {
      "title": "Commodity Purchases",
      "value": "15 items",
      "progress": 0.45,
      "subtitle": "45% of seasonal requirements",
    },
  ];

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _handleRefresh() async {
    setState(() {
      _isRefreshing = true;
    });

    // Simulate API call
    await Future.delayed(Duration(seconds: 2));

    setState(() {
      _isRefreshing = false;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Dashboard updated successfully'),
          backgroundColor: AppTheme.lightTheme.colorScheme.primary,
        ),
      );
    }
  }

  void _showQuickActions() {
    final quickActions = [
      {
        "title": "Apply Loan",
        "icon": CustomIconWidget(
          iconName: 'account_balance_wallet',
          color: AppTheme.lightTheme.colorScheme.primary,
          size: 6.w,
        ),
        "color": AppTheme.lightTheme.colorScheme.primary,
        "onTap": () {
          Navigator.pop(context);
          Navigator.pushNamed(context, '/loan-application-screen');
        },
      },
      {
        "title": "Make Deposit",
        "icon": CustomIconWidget(
          iconName: 'savings',
          color: Colors.green,
          size: 6.w,
        ),
        "color": Colors.green,
        "onTap": () {
          Navigator.pop(context);
          Navigator.pushNamed(context, '/savings-balance-sheet-screen');
        },
      },
      {
        "title": "Browse Store",
        "icon": CustomIconWidget(
          iconName: 'store',
          color: Colors.orange,
          size: 6.w,
        ),
        "color": Colors.orange,
        "onTap": () {
          Navigator.pop(context);
          Navigator.pushNamed(context, '/commodity-store-screen');
        },
      },
      {
        "title": "View Loans",
        "icon": CustomIconWidget(
          iconName: 'receipt_long',
          color: Colors.blue,
          size: 6.w,
        ),
        "color": Colors.blue,
        "onTap": () {
          Navigator.pop(context);
          Navigator.pushNamed(context, '/loan-balance-sheet-screen');
        },
      },
      {
        "title": "Pay Bills",
        "icon": CustomIconWidget(
          iconName: 'payment',
          color: Colors.purple,
          size: 6.w,
        ),
        "color": Colors.purple,
        "onTap": () {
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Bill payment feature coming soon')),
          );
        },
      },
      {
        "title": "Support",
        "icon": CustomIconWidget(
          iconName: 'help_outline',
          color: Colors.teal,
          size: 6.w,
        ),
        "color": Colors.teal,
        "onTap": () {
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Contact support: +234-800-BAFSC-HELP')),
          );
        },
      },
    ];

    QuickActionsBottomSheet.show(context, quickActions);
  }

  CustomIconWidget _getActivityIcon(String type) {
    switch (type) {
      case 'loan_payment':
        return CustomIconWidget(
          iconName: 'account_balance_wallet',
          color: Colors.red,
          size: 5.w,
        );
      case 'savings_deposit':
        return CustomIconWidget(
          iconName: 'savings',
          color: Colors.green,
          size: 5.w,
        );
      case 'commodity_purchase':
        return CustomIconWidget(
          iconName: 'shopping_cart',
          color: Colors.orange,
          size: 5.w,
        );
      case 'interest_credit':
        return CustomIconWidget(
          iconName: 'trending_up',
          color: Colors.blue,
          size: 5.w,
        );
      case 'loan_approval':
        return CustomIconWidget(
          iconName: 'check_circle',
          color: Colors.green,
          size: 5.w,
        );
      default:
        return CustomIconWidget(
          iconName: 'receipt',
          color: AppTheme.lightTheme.colorScheme.primary,
          size: 5.w,
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return AuroraBackground(
      child: Scaffold(
        backgroundColor: colorScheme.surface,
        body: SafeArea(
          child: RefreshIndicator(
            onRefresh: _handleRefresh,
            color: colorScheme.primary,
            child: CustomScrollView(
              controller: _scrollController,
              slivers: [
                // Welcome Header
                SliverToBoxAdapter(
                  child: WelcomeHeaderWidget(
                    memberName: memberData['name'] as String,
                    hasNotifications: memberData['hasNotifications'] as bool,
                    onNotificationTap: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('You have 3 new notifications'),
                          action: SnackBarAction(
                            label: 'View',
                            onPressed: () {},
                          ),
                        ),
                      );
                    },
                  ),
                ),

                // Balance Cards
                // ...existing code for SliverToBoxAdapter, PageView, and BalanceCardWidget...

                // Quick Stats
                SliverToBoxAdapter(
                  child: QuickStatsWidget(statsData: monthlyStats),
                ),

                // Recent Activity Header
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Recent Activity',
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: colorScheme.onSurface,
                          ),
                        ),
                        TextButton(
                          onPressed: () {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text('View all activities')),
                            );
                          },
                          child: Text(
                            'View All',
                            style: theme.textTheme.labelLarge?.copyWith(
                              color: colorScheme.primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Recent Activities List
                SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final activity = recentActivities[index];
                      return ActivityItemWidget(
                        title: activity['title'] as String,
                        description: activity['description'] as String,
                        amount: activity['amount'] is num ? formatNaira(activity['amount']) : activity['amount'] as String,
                        timestamp: activity['timestamp'] as String,
                        icon: _getActivityIcon(activity['type'] as String),
                        onTap: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Activity details: ${activity['title']}'),
                            ),
                          );
                        },
                      );
                    },
                    childCount: recentActivities.length,
                  ),
                ),

                // Bottom spacing
                SliverToBoxAdapter(
                  child: SizedBox(height: 10.h),
                ),
              ],
            ),
          ),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: _showQuickActions,
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          child: CustomIconWidget(
            iconName: 'add',
            color: colorScheme.onPrimary,
            size: 6.w,
          ),
        ),
        bottomNavigationBar: CustomBottomBar(
          currentIndex: CustomBottomBar.getIndexForRoute('/member-dashboard'),
          variant: CustomBottomBarVariant.standard,
        ),
      ),
    );
  }
}
