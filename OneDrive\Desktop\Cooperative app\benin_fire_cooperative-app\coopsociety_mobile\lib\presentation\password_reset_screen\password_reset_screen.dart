import 'package:flutter/material.dart';

class PasswordResetScreen extends StatelessWidget {
  const PasswordResetScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Reset Password')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: <PERSON><PERSON><PERSON>(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TextField(
              decoration: InputDecoration(labelText: 'Email'),
            ),
            Si<PERSON><PERSON><PERSON>(height: 24),
            ElevatedButton(
              onPressed: () {},
              child: Text('Send Reset Link'),
            ),
          ],
        ),
      ),
    );
  }
}
