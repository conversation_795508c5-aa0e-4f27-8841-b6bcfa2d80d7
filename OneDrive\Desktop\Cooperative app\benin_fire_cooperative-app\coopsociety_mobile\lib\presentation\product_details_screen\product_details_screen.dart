import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../providers/cart_provider.dart';
import '../../widgets/custom_app_bar.dart';

class ProductDetailsScreen extends StatelessWidget {
  const ProductDetailsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: 'Product Details',
        variant: CustomAppBarVariant.minimal,
        automaticallyImplyLeading: true,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.pushNamed(context, AppRoutes.shoppingCart);
            },
            icon: Badge(
              label: Consumer<CartProvider>(
                builder: (context, cart, _) => Text(
                  '${cart.itemCount}',
                  style: TextStyle(
                    color: AppTheme.lightTheme.colorScheme.onPrimary,
                    fontSize: 12,
                  ),
                ),
              ),
              child: Icon(
                Icons.shopping_cart,
                color: AppTheme.lightTheme.colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 40.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2.w),
                image: DecorationImage(
                  image: AssetImage('assets/images/no-image.jpg'),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            SizedBox(height: 3.h),
            Text(
              'Rice',
              style: AppTheme.lightTheme.textTheme.headlineMedium,
            ),
            SizedBox(height: 1.h),
            Text(
              'Pure Premium Nigerian Rice',
              style: AppTheme.lightTheme.textTheme.bodyLarge?.copyWith(
                color: AppTheme.lightTheme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
            SizedBox(height: 2.h),
            Text(
              formatNaira(55000),
              style: AppTheme.lightTheme.textTheme.headlineMedium?.copyWith(
                fontFamily: 'IBMPlexMono',
                color: AppTheme.lightTheme.colorScheme.primary,
              ),
            ),
            SizedBox(height: 3.h),
            Text(
              'Description',
              style: AppTheme.lightTheme.textTheme.titleLarge,
            ),
            SizedBox(height: 1.h),
            Text(
              '50kg bag of premium Nigerian rice. Grown and processed locally with high-quality standards. Perfect for daily consumption and special occasions.',
              style: AppTheme.lightTheme.textTheme.bodyLarge?.copyWith(
                color: AppTheme.lightTheme.colorScheme.onSurface.withOpacity(0.8),
              ),
            ),
            SizedBox(height: 3.h),
            SizedBox(
              width: double.infinity,
              child: Consumer<CartProvider>(
                builder: (context, cart, _) {
                  return ElevatedButton(
                    onPressed: () {
                      cart.addItem(
                        productId: '1', // This should come from the product data
                        name: 'Rice',
                        price: 55000,
                        image: 'assets/images/no-image.jpg',
                      );
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Text('Added to cart'),
                          action: SnackBarAction(
                            label: 'UNDO',
                            onPressed: () {
                              cart.removeSingleItem('1');
                            },
                          ),
                        ),
                      );
                    },
                    child: const Text('Add to Cart'),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
