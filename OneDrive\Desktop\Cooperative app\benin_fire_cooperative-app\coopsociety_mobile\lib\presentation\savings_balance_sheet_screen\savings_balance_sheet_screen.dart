import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/app_export.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/custom_bottom_bar.dart';
import './widgets/account_summary_widget.dart';
import './widgets/action_buttons_widget.dart';
import './widgets/goal_setting_widget.dart';
import './widgets/interest_breakdown_widget.dart';
import './widgets/savings_header_widget.dart';
import './widgets/search_filter_widget.dart';
import './widgets/transaction_list_widget.dart';

class SavingsBalanceSheetScreen extends StatefulWidget {
  const SavingsBalanceSheetScreen({super.key});

  @override
  State<SavingsBalanceSheetScreen> createState() =>
      _SavingsBalanceSheetScreenState();
}

class _SavingsBalanceSheetScreenState extends State<SavingsBalanceSheetScreen> {
  bool _isLoading = false;
  String _searchQuery = '';
  DateTimeRange? _dateFilter;
  double? _minAmountFilter;
  double? _maxAmountFilter;
  double? _savingsGoal = 10000.0;
  DateTime _lastSyncTime = DateTime.now();

  // Mock data for savings account
  final double _currentBalance = 8750.50;
  final double _recentInterest = 125.75;
  final DateTime _accountOpeningDate = DateTime(2022, 3, 15);
  final double _totalDeposits = 12500.00;
  final double _totalWithdrawals = 4000.00;
  final double _accumulatedInterest = 250.50;

  final List<Map<String, dynamic>> _allTransactions = [
    {
      "id": 1,
      "type": "Deposit",
      "amount": 500.00,
      "date": DateTime.now().subtract(Duration(days: 2)),
      "description": "Monthly Savings Deposit",
      "runningBalance": 8750.50,
      "referenceNumber": "DEP001234",
      "status": "Completed",
    },
    {
      "id": 2,
      "type": "Interest",
      "amount": 25.75,
      "date": DateTime.now().subtract(Duration(days: 5)),
      "description": "Monthly Interest Credit",
      "runningBalance": 8250.50,
      "referenceNumber": "INT001234",
      "status": "Completed",
    },
    {
      "id": 3,
      "type": "Withdrawal",
      "amount": 200.00,
      "date": DateTime.now().subtract(Duration(days: 8)),
      "description": "Emergency Fund Withdrawal",
      "runningBalance": 8224.75,
      "referenceNumber": "WTH001234",
      "status": "Completed",
    },
    {
      "id": 4,
      "type": "Deposit",
      "amount": 1000.00,
      "date": DateTime.now().subtract(Duration(days: 15)),
      "description": "Bonus Deposit",
      "runningBalance": 8424.75,
      "referenceNumber": "DEP001235",
      "status": "Completed",
    },
    {
      "id": 5,
      "type": "Interest",
      "amount": 22.50,
      "date": DateTime.now().subtract(Duration(days: 35)),
      "description": "Monthly Interest Credit",
      "runningBalance": 7424.75,
      "referenceNumber": "INT001235",
      "status": "Completed",
    },
    {
      "id": 6,
      "type": "Deposit",
      "amount": 750.00,
      "date": DateTime.now().subtract(Duration(days: 45)),
      "description": "Regular Savings Deposit",
      "runningBalance": 7402.25,
      "referenceNumber": "DEP001236",
      "status": "Completed",
    },
  ];

  final List<Map<String, dynamic>> _monthlyInterest = [
    {"month": DateTime(2024, 1), "amount": 45.20},
    {"month": DateTime(2024, 2), "amount": 48.75},
    {"month": DateTime(2024, 3), "amount": 52.30},
    {"month": DateTime(2024, 4), "amount": 55.80},
    {"month": DateTime(2024, 5), "amount": 58.25},
    {"month": DateTime(2024, 6), "amount": 62.40},
    {"month": DateTime(2024, 7), "amount": 65.90},
    {"month": DateTime(2024, 8), "amount": 68.75},
  ];

  final List<Map<String, dynamic>> _dividendHistory = [
    {
      "year": 2023,
      "amount": 350.00,
      "rate": 4.2,
      "distributionDate": DateTime(2024, 1, 15),
    },
    {
      "year": 2022,
      "amount": 280.00,
      "rate": 3.8,
      "distributionDate": DateTime(2023, 1, 15),
    },
  ];

  List<Map<String, dynamic>> get _filteredTransactions {
    List<Map<String, dynamic>> filtered = List.from(_allTransactions);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((transaction) {
        final description =
            (transaction['description'] as String).toLowerCase();
        final type = (transaction['type'] as String).toLowerCase();
        final amount = transaction['amount'].toString();
        final query = _searchQuery.toLowerCase();

        return description.contains(query) ||
            type.contains(query) ||
            amount.contains(query);
      }).toList();
    }

    // Apply date range filter
    if (_dateFilter != null) {
      filtered = filtered.where((transaction) {
        final transactionDate = transaction['date'] as DateTime;
        return transactionDate
                .isAfter(_dateFilter!.start.subtract(Duration(days: 1))) &&
            transactionDate.isBefore(_dateFilter!.end.add(Duration(days: 1)));
      }).toList();
    }

    // Apply amount range filter
    if (_minAmountFilter != null || _maxAmountFilter != null) {
      filtered = filtered.where((transaction) {
        final amount = transaction['amount'] as double;
        final minCheck =
            _minAmountFilter == null || amount >= _minAmountFilter!;
        final maxCheck =
            _maxAmountFilter == null || amount <= _maxAmountFilter!;
        return minCheck && maxCheck;
      }).toList();
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Savings Balance',
        variant: CustomAppBarVariant.primary,
        actions: [
          IconButton(
            onPressed: _exportStatement,
            icon: CustomIconWidget(
              iconName: 'file_download',
              color: AppTheme.lightTheme.colorScheme.onPrimary,
              size: 6.w,
            ),
          ),
          PopupMenuButton<String>(
            icon: CustomIconWidget(
              iconName: 'more_vert',
              color: AppTheme.lightTheme.colorScheme.onPrimary,
              size: 6.w,
            ),
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'refresh',
                child: Row(
                  children: [
                    CustomIconWidget(
                      iconName: 'refresh',
                      color: AppTheme.lightTheme.colorScheme.onSurface,
                      size: 5.w,
                    ),
                    SizedBox(width: 3.w),
                    Text('Refresh Data'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'statement',
                child: Row(
                  children: [
                    CustomIconWidget(
                      iconName: 'description',
                      color: AppTheme.lightTheme.colorScheme.onSurface,
                      size: 5.w,
                    ),
                    SizedBox(width: 3.w),
                    Text('Download Statement'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'help',
                child: Row(
                  children: [
                    CustomIconWidget(
                      iconName: 'help_outline',
                      color: AppTheme.lightTheme.colorScheme.onSurface,
                      size: 5.w,
                    ),
                    SizedBox(width: 3.w),
                    Text('Help & Support'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        color: AppTheme.lightTheme.colorScheme.primary,
        child: _isLoading
            ? Center(
                child: CircularProgressIndicator(
                  color: AppTheme.lightTheme.colorScheme.primary,
                ),
              )
            : SingleChildScrollView(
                physics: AlwaysScrollableScrollPhysics(),
                child: Column(
                  children: [
                    SizedBox(height: 2.h),
                    SavingsHeaderWidget(
                      currentBalance: _currentBalance,
                      recentInterest: _recentInterest,
                      lastUpdated: _formatLastUpdated(_lastSyncTime),
                    ),
                    SizedBox(height: 2.h),
                    AccountSummaryWidget(
                      openingDate: _accountOpeningDate,
                      totalDeposits: _totalDeposits,
                      totalWithdrawals: _totalWithdrawals,
                      accumulatedInterest: _accumulatedInterest,
                      depositProgress: _savingsGoal != null
                          ? (_currentBalance / _savingsGoal!)
                          : 0.0,
                    ),
                    SizedBox(height: 2.h),
                    GoalSettingWidget(
                      currentSavings: _currentBalance,
                      currentGoal: _savingsGoal,
                      onGoalSet: (goal) {
                        setState(() {
                          _savingsGoal = goal;
                        });
                      },
                    ),
                    SizedBox(height: 2.h),
                    SearchFilterWidget(
                      onSearchChanged: (query) {
                        setState(() {
                          _searchQuery = query;
                        });
                      },
                      onDateRangeChanged: (range) {
                        setState(() {
                          _dateFilter = range;
                        });
                      },
                      onAmountRangeChanged: (min, max) {
                        setState(() {
                          _minAmountFilter = min;
                          _maxAmountFilter = max;
                        });
                      },
                    ),
                    SizedBox(height: 2.h),
                    TransactionListWidget(
                      transactions: _filteredTransactions,
                      onTransactionTap: _showTransactionDetails,
                    ),
                    SizedBox(height: 2.h),
                    InterestBreakdownWidget(
                      monthlyInterest: _monthlyInterest,
                      dividendHistory: _dividendHistory,
                    ),
                    SizedBox(height: 2.h),
                    ActionButtonsWidget(
                      onAddDeposit: _showDepositDialog,
                      onRequestWithdrawal: _showWithdrawalDialog,
                    ),
                    SizedBox(height: 10.h), // Bottom padding for navigation
                  ],
                ),
              ),
      ),
      bottomNavigationBar: CustomBottomBar(
        currentIndex:
            CustomBottomBar.getIndexForRoute('/savings-balance-sheet-screen'),
        variant: CustomBottomBarVariant.standard,
      ),
    );
  }

  Future<void> _refreshData() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    await Future.delayed(Duration(seconds: 2));

    setState(() {
      _isLoading = false;
      _lastSyncTime = DateTime.now();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Savings data refreshed successfully'),
        backgroundColor: AppTheme.lightTheme.colorScheme.secondary,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'refresh':
        _refreshData();
        break;
      case 'statement':
        _exportStatement();
        break;
      case 'help':
        _showHelpDialog();
        break;
    }
  }

  void _exportStatement() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Generating savings statement...'),
        backgroundColor: AppTheme.lightTheme.colorScheme.primary,
        action: SnackBarAction(
          label: 'View',
          textColor: AppTheme.lightTheme.colorScheme.onPrimary,
          onPressed: () {
            // Handle statement view
          },
        ),
      ),
    );
  }

  void _showTransactionDetails(Map<String, dynamic> transaction) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 50.h,
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.colorScheme.surface,
          borderRadius: BorderRadius.vertical(top: Radius.circular(5.w)),
        ),
        child: Padding(
          padding: EdgeInsets.all(4.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                  width: 12.w,
                  height: 0.5.h,
                  decoration: BoxDecoration(
                    color: AppTheme.lightTheme.colorScheme.outline
                        .withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(1.w),
                  ),
                ),
              ),
              SizedBox(height: 3.h),
              Text(
                'Transaction Details',
                style: GoogleFonts.inter(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.lightTheme.colorScheme.onSurface,
                ),
              ),
              SizedBox(height: 3.h),
              _buildDetailRow('Type', transaction['type'] as String),
              _buildDetailRow('Amount',
                  '\$${(transaction['amount'] as double).toStringAsFixed(2)}'),
              _buildDetailRow(
                  'Description', transaction['description'] as String),
              _buildDetailRow('Date',
                  _formatTransactionDate(transaction['date'] as DateTime)),
              _buildDetailRow(
                  'Reference', transaction['referenceNumber'] as String),
              _buildDetailRow('Status', transaction['status'] as String),
              _buildDetailRow('Running Balance',
                  '\$${(transaction['runningBalance'] as double).toStringAsFixed(2)}'),
              Spacer(),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text('Close'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 1.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 30.w,
            child: Text(
              label,
              style: GoogleFonts.inter(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: AppTheme.lightTheme.colorScheme.onSurface
                    .withValues(alpha: 0.7),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.inter(
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
                color: AppTheme.lightTheme.colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showDepositDialog() {
    final TextEditingController amountController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Add Deposit',
          style: GoogleFonts.inter(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: amountController,
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              decoration: InputDecoration(
                labelText: 'Amount',
                prefixText: '\$',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 2.h),
            TextField(
              controller: descriptionController,
              decoration: InputDecoration(
                labelText: 'Description (Optional)',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final amount = double.tryParse(amountController.text);
              if (amount != null && amount > 0) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                        'Deposit request submitted for \$${amount.toStringAsFixed(2)}'),
                    backgroundColor: AppTheme.lightTheme.colorScheme.secondary,
                  ),
                );
              }
            },
            child: Text('Submit'),
          ),
        ],
      ),
    );
  }

  void _showWithdrawalDialog() {
    final TextEditingController amountController = TextEditingController();
    final TextEditingController reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Request Withdrawal',
          style: GoogleFonts.inter(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: amountController,
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              decoration: InputDecoration(
                labelText: 'Amount',
                prefixText: '\$',
                border: OutlineInputBorder(),
                helperText:
                    'Available: \$${_currentBalance.toStringAsFixed(2)}',
              ),
            ),
            SizedBox(height: 2.h),
            TextField(
              controller: reasonController,
              decoration: InputDecoration(
                labelText: 'Reason for Withdrawal',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final amount = double.tryParse(amountController.text);
              if (amount != null && amount > 0 && amount <= _currentBalance) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                        'Withdrawal request submitted for \$${amount.toStringAsFixed(2)}'),
                    backgroundColor: AppTheme.lightTheme.colorScheme.primary,
                  ),
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content:
                        Text('Please enter a valid amount within your balance'),
                    backgroundColor: AppTheme.lightTheme.colorScheme.error,
                  ),
                );
              }
            },
            child: Text('Submit'),
          ),
        ],
      ),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Help & Support',
          style: GoogleFonts.inter(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Savings Account Features:',
              style: GoogleFonts.inter(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              '• View your current balance and transaction history\n'
              '• Track interest earnings and dividend distributions\n'
              '• Set and monitor savings goals\n'
              '• Request deposits and withdrawals\n'
              '• Export account statements\n'
              '• Filter transactions by date and amount',
              style: GoogleFonts.inter(
                fontSize: 12.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
            SizedBox(height: 2.h),
            Text(
              'Need more help? Contact our support <NAME_EMAIL>',
              style: GoogleFonts.inter(
                fontSize: 12.sp,
                fontWeight: FontWeight.w400,
                color: AppTheme.lightTheme.colorScheme.primary,
              ),
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Got it'),
          ),
        ],
      ),
    );
  }

  String _formatLastUpdated(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} min ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hr ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  String _formatTransactionDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} at ${_formatTime(date)}';
  }

  String _formatTime(DateTime date) {
    final hour = date.hour > 12
        ? date.hour - 12
        : date.hour == 0
            ? 12
            : date.hour;
    final minute = date.minute.toString().padLeft(2, '0');
    final period = date.hour >= 12 ? 'PM' : 'AM';
    return '$hour:$minute $period';
  }
}