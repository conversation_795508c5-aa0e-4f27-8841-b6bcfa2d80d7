import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../core/app_export.dart';
import '../../../core/utils/currency_utils.dart';

class AccountSummaryWidget extends StatelessWidget {
  final DateTime openingDate;
  final double totalDeposits;
  final double totalWithdrawals;
  final double accumulatedInterest;
  final double depositProgress;

  const AccountSummaryWidget({
    super.key,
    required this.openingDate,
    required this.totalDeposits,
    required this.totalWithdrawals,
    required this.accumulatedInterest,
    required this.depositProgress,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1.0,
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(3.w),
      ),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CustomIconWidget(
                  iconName: 'account_balance',
                  color: AppTheme.lightTheme.colorScheme.primary,
                  size: 6.w,
                ),
                SizedBox(width: 3.w),
                Text(
                  'Account Summary',
                  style: GoogleFonts.inter(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.lightTheme.colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            SizedBox(height: 3.h),
            _buildSummaryRow(
              'Account Opened',
              '${openingDate.day}/${openingDate.month}/${openingDate.year}',
              Icons.calendar_today,
            ),
            SizedBox(height: 2.h),
            _buildSummaryRow(
              'Total Deposits',
              formatNaira(totalDeposits),
              Icons.arrow_downward,
              valueColor: AppTheme.lightTheme.colorScheme.secondary,
            ),
            SizedBox(height: 2.h),
            _buildSummaryRow(
              'Total Withdrawals',
              formatNaira(totalWithdrawals),
              Icons.arrow_upward,
              valueColor: AppTheme.lightTheme.colorScheme.error,
            ),
            SizedBox(height: 2.h),
            _buildSummaryRow(
              'Interest Earned',
              formatNaira(accumulatedInterest),
              Icons.trending_up,
              valueColor: AppTheme.lightTheme.colorScheme.tertiary,
            ),
            SizedBox(height: 3.h),
            Text(
              'Savings Progress',
              style: GoogleFonts.inter(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: AppTheme.lightTheme.colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 1.h),
            LinearProgressIndicator(
              value: depositProgress.clamp(0.0, 1.0),
              backgroundColor: AppTheme.lightTheme.colorScheme.outline
                  .withValues(alpha: 0.3),
              valueColor: AlwaysStoppedAnimation<Color>(
                AppTheme.lightTheme.colorScheme.primary,
              ),
              minHeight: 1.5.h,
            ),
            SizedBox(height: 1.h),
            Text(
              '${(depositProgress * 100).toStringAsFixed(1)}% of annual goal',
              style: GoogleFonts.inter(
                fontSize: 12.sp,
                fontWeight: FontWeight.w400,
                color: AppTheme.lightTheme.colorScheme.onSurface
                    .withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(
    String label,
    String value,
    IconData icon, {
    Color? valueColor,
  }) {
    return Row(
      children: [
        CustomIconWidget(
          iconName: icon.toString().split('.').last,
          color:
              AppTheme.lightTheme.colorScheme.onSurface.withValues(alpha: 0.6),
          size: 4.w,
        ),
        SizedBox(width: 3.w),
        Expanded(
          child: Text(
            label,
            style: GoogleFonts.inter(
              fontSize: 13.sp,
              fontWeight: FontWeight.w400,
              color: AppTheme.lightTheme.colorScheme.onSurface
                  .withValues(alpha: 0.7),
            ),
          ),
        ),
        Text(
          value,
          style: GoogleFonts.inter(
            fontSize: 13.sp,
            fontWeight: FontWeight.w600,
            color: valueColor ?? AppTheme.lightTheme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }
}