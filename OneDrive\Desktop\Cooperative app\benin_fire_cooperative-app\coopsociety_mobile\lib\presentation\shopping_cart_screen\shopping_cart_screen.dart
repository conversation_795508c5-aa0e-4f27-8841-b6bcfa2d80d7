import 'package:flutter/material.dart';
import '../../widgets/aurora_background.dart';

class ShoppingCartScreen extends StatelessWidget {
  const ShoppingCartScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AuroraBackground(
      child: Scaffold(
        appBar: AppBar(title: Text('Shopping Cart')),
        body: ListView(
          padding: EdgeInsets.all(16),
          children: [
            ListTile(
              title: Text('Bag of Rice'),
              subtitle: Text('₦55,000'),
              trailing: Text('x1'),
            ),
            ListTile(
              title: Text('Cow Meat'),
              subtitle: Text('₦3,500'),
              trailing: Text('x2'),
            ),
            ListTile(
              title: Text('Indomie Noddles in Cartons'),
              subtitle: Text('₦9,000'),
              trailing: Text('x1'),
            ),
            ListTile(
              title: Text('Vegetable Oil 5 liters available in Cartons'),
              subtitle: Text('₦42,000'),
              trailing: Text('x1'),
            ),
            ListTile(
              title: Text('Tomatoe Paste In Cartons'),
              subtitle: Text('₦8,000'),
              trailing: Text('x3'),
            ),
            ListTile(
              title: Text('Okomu Oil available in Catorn'),
              subtitle: Text('₦48,000'),
              trailing: Text('x1'),
            ),
          ],
        ),
      ),
    );
  }
}
