import 'package:flutter/material.dart';

class CartItem {
  final dynamic productId;
  final String name;
  final double price;
  final int quantity;
  final String image;

  CartItem({
    required this.productId,
    required this.name,
    required this.price,
    required this.quantity,
    required this.image,
  });

  double get total => price * quantity;
}

class CartProvider extends ChangeNotifier {
  final Map<dynamic, CartItem> _items = {};

  Map<dynamic, CartItem> get items => Map.unmodifiable(_items);

  int get itemCount => _items.values.fold(0, (sum, item) => sum + item.quantity);

  double get totalAmount =>
      _items.values.fold(0, (sum, item) => sum + item.total);

  void addItem({
    required dynamic productId,
    required String name,
    required double price,
    required String image,
  }) {
    if (_items.containsKey(productId)) {
      _items.update(
        productId,
        (existingItem) => CartItem(
          productId: existingItem.productId,
          name: existingItem.name,
          price: existingItem.price,
          quantity: existingItem.quantity + 1,
          image: existingItem.image,
        ),
      );
    } else {
      _items.putIfAbsent(
        productId,
        () => CartItem(
          productId: productId,
          name: name,
          price: price,
          quantity: 1,
          image: image,
        ),
      );
    }
    notifyListeners();
  }

  void removeItem(dynamic productId) {
    _items.remove(productId);
    notifyListeners();
  }

  void removeSingleItem(dynamic productId) {
    if (!_items.containsKey(productId)) return;

    if (_items[productId]!.quantity > 1) {
      _items.update(
        productId,
        (existingItem) => CartItem(
          productId: existingItem.productId,
          name: existingItem.name,
          price: existingItem.price,
          quantity: existingItem.quantity - 1,
          image: existingItem.image,
        ),
      );
    } else {
      _items.remove(productId);
    }
    notifyListeners();
  }

  void clear() {
    _items.clear();
    notifyListeners();
  }
}
