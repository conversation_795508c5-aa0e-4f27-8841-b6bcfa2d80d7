import 'package:flutter/material.dart';
import '../presentation/loan_balance_sheet_screen/loan_balance_sheet_screen.dart';
import '../presentation/commodity_store_screen/commodity_store_screen.dart';
import '../presentation/member_dashboard/member_dashboard.dart';
import '../presentation/login_screen/login_screen.dart';
import '../presentation/splash_screen/splash_screen.dart';
import '../presentation/loan_application_screen/loan_application_screen.dart';
import '../presentation/savings_balance_sheet_screen/savings_balance_sheet_screen.dart';
import '../presentation/registration_screen/registration_screen.dart';
import '../presentation/shopping_cart_screen/shopping_cart_screen.dart';
import '../presentation/member_profile_screen/member_profile_screen.dart';
import '../presentation/product_details_screen/product_details_screen.dart';
import '../presentation/password_reset_screen/password_reset_screen.dart';

class AppRoutes {
  static const String registration = '/registration';
  static const String shoppingCart = '/shopping-cart';
  static const String memberProfile = '/member-profile';
  static const String productDetails = '/product-details';
  static const String passwordReset = '/password-reset';
  // TODO: Add your routes here
  static const String initial = '/';
  static const String splash = '/splash';
  static const String loanBalanceSheet = '/loan-balance-sheet-screen';
  static const String commodityStore = '/commodity-store-screen';
  static const String memberDashboard = '/member-dashboard';
  static const String login = '/login';
  static const String loanApplication = '/loan-application-screen';
  static const String savingsBalanceSheet = '/savings-balance-sheet-screen';

  static Map<String, WidgetBuilder> routes = {
    initial: (context) => SplashScreen(),
    splash: (context) => SplashScreen(),
    loanBalanceSheet: (context) => const LoanBalanceSheetScreen(),
    commodityStore: (context) => const CommodityStoreScreen(),
    memberDashboard: (context) => const MemberDashboard(),
  login: (context) => const LoginScreen(),
    loanApplication: (context) => const LoanApplicationScreen(),
    savingsBalanceSheet: (context) => const SavingsBalanceSheetScreen(),
  registration: (context) => const RegistrationScreen(),
  shoppingCart: (context) => const ShoppingCartScreen(),
  memberProfile: (context) => const MemberProfileScreen(),
  productDetails: (context) => const ProductDetailsScreen(),
  passwordReset: (context) => const PasswordResetScreen(),
  // TODO: Add your other routes here
  };
}
