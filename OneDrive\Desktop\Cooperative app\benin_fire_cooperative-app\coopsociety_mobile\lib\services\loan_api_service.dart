import 'dart:convert';
import 'package:http/http.dart' as http;

class LoanApiService {
  static const String _baseUrl = 'https://api.bafsc.com/v1'; // Replace with actual API URL

  // Submit loan application
  static Future<Map<String, dynamic>> submitLoanApplication({
    required double amount,
    required int duration,
    required String purpose,
    required List<String> documentUrls,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/loans/apply'),
        headers: {
          'Content-Type': 'application/json',
          // TODO: Add authorization header
          'Authorization': 'Bearer YOUR_AUTH_TOKEN',
        },
        body: jsonEncode({
          'amount': amount,
          'duration': duration,
          'purpose': purpose,
          'documentUrls': documentUrls,
          'status': 'pending',
          'submittedAt': DateTime.now().toIso8601String(),
        }),
      );

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to submit loan application: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to submit loan application: $e');
    }
  }

  // Upload document
  static Future<String> uploadDocument(String filePath) async {
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$_baseUrl/documents/upload'),
      );

      request.files.add(await http.MultipartFile.fromPath(
        'document',
        filePath,
      ));

      request.headers.addAll({
        // TODO: Add authorization header
        'Authorization': 'Bearer YOUR_AUTH_TOKEN',
      });

      var response = await request.send();
      var responseData = await response.stream.bytesToString();

      if (response.statusCode == 201) {
        var data = jsonDecode(responseData);
        return data['url'];
      } else {
        throw Exception('Failed to upload document: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to upload document: $e');
    }
  }

  // Get loan balance sheet
  static Future<Map<String, dynamic>> getLoanBalanceSheet() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/loans/balance-sheet'),
        headers: {
          // TODO: Add authorization header
          'Authorization': 'Bearer YOUR_AUTH_TOKEN',
        },
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to fetch loan balance sheet: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch loan balance sheet: $e');
    }
  }

  // Get loan details
  static Future<Map<String, dynamic>> getLoanDetails(String loanId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/loans/$loanId'),
        headers: {
          // TODO: Add authorization header
          'Authorization': 'Bearer YOUR_AUTH_TOKEN',
        },
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to fetch loan details: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch loan details: $e');
    }
  }

  // Get payment history
  static Future<List<Map<String, dynamic>>> getPaymentHistory(String loanId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/loans/$loanId/payments'),
        headers: {
          // TODO: Add authorization header
          'Authorization': 'Bearer YOUR_AUTH_TOKEN',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data['payments']);
      } else {
        throw Exception('Failed to fetch payment history: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch payment history: $e');
    }
  }
}
