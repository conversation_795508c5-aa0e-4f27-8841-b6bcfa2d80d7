import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:simple_animations/simple_animations.dart';

class AuroraBackground extends StatelessWidget {
  final Widget child;
  const AuroraBackground({required this.child, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: MirrorAnimationBuilder<double>(
            tween: Tween(begin: 0.0, end: 2 * 3.14159),
            duration: const Duration(seconds: 18),
            curve: Curves.easeInOut,
            builder: (context, value, child) {
              return CustomPaint(
                painter: _AuroraPainter(value),
              );
            },
          ),
        ),
        child,
      ],
    );
  }
}

class _AuroraPainter extends CustomPainter {
  final double animationValue;
  _AuroraPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Offset.zero & size;
    final gradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        Colors.purple.withOpacity(0.5 + 0.2 * (0.5 + 0.5 * math.sin(animationValue))),
        Colors.blue.withOpacity(0.5 + 0.2 * (0.5 + 0.5 * math.sin(animationValue + 1))),
        Colors.teal.withOpacity(0.5 + 0.2 * (0.5 + 0.5 * math.sin(animationValue + 2))),
        Colors.pink.withOpacity(0.5 + 0.2 * (0.5 + 0.5 * math.sin(animationValue + 3))),
      ],
    );
    final paint = Paint()..shader = gradient.createShader(rect);
    canvas.drawRect(rect, paint);
  }

  @override
  bool shouldRepaint(covariant _AuroraPainter oldDelegate) => true;
}
