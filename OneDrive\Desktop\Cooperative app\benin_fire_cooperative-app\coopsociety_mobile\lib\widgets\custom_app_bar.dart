import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

enum CustomAppBarVariant {
  primary,
  secondary,
  transparent,
  minimal,
}

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final CustomAppBarVariant variant;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final bool centerTitle;
  final double? elevation;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final PreferredSizeWidget? bottom;
  final VoidCallback? onBackPressed;

  const CustomAppBar({
    super.key,
    required this.title,
    this.variant = CustomAppBarVariant.primary,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.centerTitle = true,
    this.elevation,
    this.backgroundColor,
    this.foregroundColor,
    this.bottom,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    Color effectiveBackgroundColor;
    Color effectiveForegroundColor;
    double effectiveElevation;

    switch (variant) {
      case CustomAppBarVariant.primary:
        effectiveBackgroundColor = backgroundColor ?? colorScheme.primary;
        effectiveForegroundColor = foregroundColor ?? colorScheme.onPrimary;
        effectiveElevation = elevation ?? 1.0;
        break;
      case CustomAppBarVariant.secondary:
        effectiveBackgroundColor = backgroundColor ?? colorScheme.secondary;
        effectiveForegroundColor = foregroundColor ?? colorScheme.onSecondary;
        effectiveElevation = elevation ?? 1.0;
        break;
      case CustomAppBarVariant.transparent:
        effectiveBackgroundColor = backgroundColor ?? Colors.transparent;
        effectiveForegroundColor = foregroundColor ?? colorScheme.onSurface;
        effectiveElevation = elevation ?? 0.0;
        break;
      case CustomAppBarVariant.minimal:
        effectiveBackgroundColor = backgroundColor ?? colorScheme.surface;
        effectiveForegroundColor = foregroundColor ?? colorScheme.onSurface;
        effectiveElevation = elevation ?? 0.0;
        break;
    }

    return AppBar(
      title: Text(
        title,
        style: GoogleFonts.inter(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: effectiveForegroundColor,
          letterSpacing: 0.15,
        ),
      ),
      backgroundColor: effectiveBackgroundColor,
      foregroundColor: effectiveForegroundColor,
      elevation: effectiveElevation,
      centerTitle: centerTitle,
      automaticallyImplyLeading: automaticallyImplyLeading,
      leading: leading ??
          (automaticallyImplyLeading && Navigator.canPop(context)
              ? IconButton(
                  icon: Icon(
                    Icons.arrow_back_ios,
                    color: effectiveForegroundColor,
                  ),
                  onPressed: onBackPressed ?? () => Navigator.pop(context),
                )
              : null),
      actions: _buildActions(context, effectiveForegroundColor),
      bottom: bottom,
      shadowColor: theme.shadowColor,
      surfaceTintColor: Colors.transparent,
      iconTheme: IconThemeData(color: effectiveForegroundColor),
      actionsIconTheme: IconThemeData(color: effectiveForegroundColor),
    );
  }

  List<Widget>? _buildActions(BuildContext context, Color foregroundColor) {
    if (actions != null) return actions;

    // Default actions based on current route
    final currentRoute = ModalRoute.of(context)?.settings.name;

    switch (currentRoute) {
      case '/member-dashboard':
        return [
          IconButton(
            icon: Icon(Icons.notifications_outlined, color: foregroundColor),
            onPressed: () {
              // Handle notifications
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Notifications')),
              );
            },
          ),
          PopupMenuButton<String>(
            icon: Icon(Icons.more_vert, color: foregroundColor),
            onSelected: (value) {
              switch (value) {
                case 'profile':
                  // Navigate to profile
                  break;
                case 'settings':
                  // Navigate to settings
                  break;
                case 'logout':
                  Navigator.pushNamedAndRemoveUntil(
                    context,
                    '/login-screen',
                    (route) => false,
                  );
                  break;
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'profile',
                child: Row(
                  children: [
                    Icon(Icons.person_outline, size: 20),
                    SizedBox(width: 12),
                    Text('Profile'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings_outlined, size: 20),
                    SizedBox(width: 12),
                    Text('Settings'),
                  ],
                ),
              ),
              PopupMenuDivider(),
              PopupMenuItem(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(Icons.logout,
                        size: 20, color: Theme.of(context).colorScheme.error),
                    SizedBox(width: 12),
                    Text('Logout',
                        style: TextStyle(
                            color: Theme.of(context).colorScheme.error)),
                  ],
                ),
              ),
            ],
          ),
        ];
      case '/loan-application-screen':
        return [
          IconButton(
            icon: Icon(Icons.help_outline, color: foregroundColor),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Loan Application Help')),
              );
            },
          ),
        ];
      case '/commodity-store-screen':
        return [
          IconButton(
            icon: Icon(Icons.search, color: foregroundColor),
            onPressed: () {
              // Handle search
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Search Commodities')),
              );
            },
          ),
          IconButton(
            icon: Icon(Icons.shopping_cart_outlined, color: foregroundColor),
            onPressed: () {
              // Handle cart
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Shopping Cart')),
              );
            },
          ),
        ];
      default:
        return null;
    }
  }

  @override
  Size get preferredSize => Size.fromHeight(
        kToolbarHeight + (bottom?.preferredSize.height ?? 0.0),
      );
}
