import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

enum CustomBottomBarVariant {
  standard,
  floating,
  minimal,
}

class CustomBottomBar extends StatelessWidget {
  final int currentIndex;
  final ValueChanged<int>? onTap;
  final CustomBottomBarVariant variant;
  final Color? backgroundColor;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;
  final double? elevation;

  const CustomBottomBar({
    super.key,
    required this.currentIndex,
    this.onTap,
    this.variant = CustomBottomBarVariant.standard,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
    this.elevation,
  });

  static const List<_BottomBarItem> _items = [
    _BottomBarItem(
      icon: Icons.dashboard_outlined,
      activeIcon: Icons.dashboard,
      label: 'Dashboard',
      route: '/member-dashboard',
    ),
    _BottomBarItem(
      icon: Icons.account_balance_wallet_outlined,
      activeIcon: Icons.account_balance_wallet,
      label: 'Loans',
      route: '/loan-balance-sheet-screen',
    ),
    _BottomBarItem(
      icon: Icons.savings_outlined,
      activeIcon: Icons.savings,
      label: 'Savings',
      route: '/savings-balance-sheet-screen',
    ),
    _BottomBarItem(
      icon: Icons.store_outlined,
      activeIcon: Icons.store,
      label: 'Store',
      route: '/commodity-store-screen',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    Color effectiveBackgroundColor;
    Color effectiveSelectedItemColor;
    Color effectiveUnselectedItemColor;
    double effectiveElevation;

    switch (variant) {
      case CustomBottomBarVariant.standard:
        effectiveBackgroundColor = backgroundColor ?? colorScheme.surface;
        effectiveSelectedItemColor = selectedItemColor ?? colorScheme.primary;
        effectiveUnselectedItemColor =
            unselectedItemColor ?? colorScheme.onSurface.withValues(alpha: 0.6);
        effectiveElevation = elevation ?? 4.0;
        break;
      case CustomBottomBarVariant.floating:
        effectiveBackgroundColor = backgroundColor ?? colorScheme.surface;
        effectiveSelectedItemColor = selectedItemColor ?? colorScheme.primary;
        effectiveUnselectedItemColor =
            unselectedItemColor ?? colorScheme.onSurface.withValues(alpha: 0.6);
        effectiveElevation = elevation ?? 8.0;
        break;
      case CustomBottomBarVariant.minimal:
        effectiveBackgroundColor = backgroundColor ?? colorScheme.surface;
        effectiveSelectedItemColor = selectedItemColor ?? colorScheme.primary;
        effectiveUnselectedItemColor =
            unselectedItemColor ?? colorScheme.onSurface.withValues(alpha: 0.4);
        effectiveElevation = elevation ?? 0.0;
        break;
    }

    Widget bottomBar = BottomNavigationBar(
      currentIndex: currentIndex,
      onTap: (index) {
        if (onTap != null) {
          onTap!(index);
        } else {
          _handleNavigation(context, index);
        }
      },
      type: BottomNavigationBarType.fixed,
      backgroundColor: effectiveBackgroundColor,
      selectedItemColor: effectiveSelectedItemColor,
      unselectedItemColor: effectiveUnselectedItemColor,
      elevation: effectiveElevation,
      selectedLabelStyle: GoogleFonts.inter(
        fontSize: 12,
        fontWeight: FontWeight.w500,
      ),
      unselectedLabelStyle: GoogleFonts.inter(
        fontSize: 12,
        fontWeight: FontWeight.w400,
      ),
      items: _items
          .map((item) => BottomNavigationBarItem(
                icon: Padding(
                  padding: EdgeInsets.only(bottom: 4.0),
                  child: Icon(
                    currentIndex == _items.indexOf(item)
                        ? item.activeIcon
                        : item.icon,
                    size: 24,
                  ),
                ),
                label: item.label,
                tooltip: item.label,
              ))
          .toList(),
    );

    if (variant == CustomBottomBarVariant.floating) {
      return Container(
        margin: EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.0),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withValues(alpha: 0.1),
              blurRadius: 8.0,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16.0),
          child: bottomBar,
        ),
      );
    }

    return bottomBar;
  }

  void _handleNavigation(BuildContext context, int index) {
    if (index < 0 || index >= _items.length) return;

    final targetRoute = _items[index].route;
    final currentRoute = ModalRoute.of(context)?.settings.name;

    if (targetRoute != currentRoute) {
      Navigator.pushNamedAndRemoveUntil(
        context,
        targetRoute,
        (route) => route.isFirst,
      );
    }
  }

  static int getIndexForRoute(String? route) {
    for (int i = 0; i < _items.length; i++) {
      if (_items[i].route == route) {
        return i;
      }
    }
    return 0; // Default to dashboard
  }
}

class _BottomBarItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;
  final String route;

  const _BottomBarItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
    required this.route,
  });
}
