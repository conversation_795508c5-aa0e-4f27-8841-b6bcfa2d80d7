import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

enum CustomTabBarVariant {
  primary,
  secondary,
  minimal,
  pills,
}

class CustomTabBar extends StatelessWidget implements PreferredSizeWidget {
  final List<String> tabs;
  final TabController? controller;
  final CustomTabBarVariant variant;
  final Color? backgroundColor;
  final Color? indicatorColor;
  final Color? labelColor;
  final Color? unselectedLabelColor;
  final bool isScrollable;
  final EdgeInsetsGeometry? labelPadding;
  final ValueChanged<int>? onTap;

  const CustomTabBar({
    super.key,
    required this.tabs,
    this.controller,
    this.variant = CustomTabBarVariant.primary,
    this.backgroundColor,
    this.indicatorColor,
    this.labelColor,
    this.unselectedLabelColor,
    this.isScrollable = false,
    this.labelPadding,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    Color effectiveBackgroundColor;
    Color effectiveIndicatorColor;
    Color effectiveLabelColor;
    Color effectiveUnselectedLabelColor;
    Decoration? indicator;
    EdgeInsetsGeometry effectiveLabelPadding;

    switch (variant) {
      case CustomTabBarVariant.primary:
        effectiveBackgroundColor = backgroundColor ?? colorScheme.primary;
        effectiveIndicatorColor = indicatorColor ?? colorScheme.onPrimary;
        effectiveLabelColor = labelColor ?? colorScheme.onPrimary;
        effectiveUnselectedLabelColor = unselectedLabelColor ??
            colorScheme.onPrimary.withValues(alpha: 0.7);
        effectiveLabelPadding =
            labelPadding ?? EdgeInsets.symmetric(horizontal: 16.0);
        indicator = UnderlineTabIndicator(
          borderSide: BorderSide(color: effectiveIndicatorColor, width: 2.0),
        );
        break;
      case CustomTabBarVariant.secondary:
        effectiveBackgroundColor = backgroundColor ?? colorScheme.surface;
        effectiveIndicatorColor = indicatorColor ?? colorScheme.primary;
        effectiveLabelColor = labelColor ?? colorScheme.primary;
        effectiveUnselectedLabelColor = unselectedLabelColor ??
            colorScheme.onSurface.withValues(alpha: 0.6);
        effectiveLabelPadding =
            labelPadding ?? EdgeInsets.symmetric(horizontal: 16.0);
        indicator = UnderlineTabIndicator(
          borderSide: BorderSide(color: effectiveIndicatorColor, width: 2.0),
        );
        break;
      case CustomTabBarVariant.minimal:
        effectiveBackgroundColor = backgroundColor ?? Colors.transparent;
        effectiveIndicatorColor = indicatorColor ?? colorScheme.primary;
        effectiveLabelColor = labelColor ?? colorScheme.onSurface;
        effectiveUnselectedLabelColor = unselectedLabelColor ??
            colorScheme.onSurface.withValues(alpha: 0.6);
        effectiveLabelPadding =
            labelPadding ?? EdgeInsets.symmetric(horizontal: 12.0);
        indicator = UnderlineTabIndicator(
          borderSide: BorderSide(color: effectiveIndicatorColor, width: 1.0),
        );
        break;
      case CustomTabBarVariant.pills:
        effectiveBackgroundColor = backgroundColor ?? colorScheme.surface;
        effectiveIndicatorColor = indicatorColor ?? colorScheme.primary;
        effectiveLabelColor = labelColor ?? colorScheme.onPrimary;
        effectiveUnselectedLabelColor = unselectedLabelColor ??
            colorScheme.onSurface.withValues(alpha: 0.6);
        effectiveLabelPadding = labelPadding ??
            EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0);
        indicator = BoxDecoration(
          color: effectiveIndicatorColor,
          borderRadius: BorderRadius.circular(20.0),
        );
        break;
    }

    return Container(
      color: effectiveBackgroundColor,
      child: TabBar(
        controller: controller,
        tabs: tabs
            .map((tab) => Tab(
                  text: tab,
                  height: variant == CustomTabBarVariant.pills ? 40.0 : null,
                ))
            .toList(),
        isScrollable: isScrollable,
        indicator: indicator,
        labelColor: effectiveLabelColor,
        unselectedLabelColor: effectiveUnselectedLabelColor,
        labelPadding: effectiveLabelPadding,
        labelStyle: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w400,
        ),
        indicatorSize: variant == CustomTabBarVariant.pills
            ? TabBarIndicatorSize.tab
            : TabBarIndicatorSize.label,
        onTap: onTap,
        splashFactory: variant == CustomTabBarVariant.pills
            ? NoSplash.splashFactory
            : null,
        overlayColor: variant == CustomTabBarVariant.pills
            ? WidgetStateProperty.all(Colors.transparent)
            : null,
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
        variant == CustomTabBarVariant.pills ? 56.0 : kToolbarHeight,
      );

  // Factory constructors for common use cases
  static CustomTabBar forLoanDetails({
    TabController? controller,
    CustomTabBarVariant variant = CustomTabBarVariant.secondary,
    ValueChanged<int>? onTap,
  }) {
    return CustomTabBar(
      tabs: ['Overview', 'Payments', 'Documents'],
      controller: controller,
      variant: variant,
      onTap: onTap,
    );
  }

  static CustomTabBar forSavingsDetails({
    TabController? controller,
    CustomTabBarVariant variant = CustomTabBarVariant.secondary,
    ValueChanged<int>? onTap,
  }) {
    return CustomTabBar(
      tabs: ['Balance', 'Transactions', 'Interest'],
      controller: controller,
      variant: variant,
      onTap: onTap,
    );
  }

  static CustomTabBar forCommodityStore({
    TabController? controller,
    CustomTabBarVariant variant = CustomTabBarVariant.pills,
    ValueChanged<int>? onTap,
  }) {
    return CustomTabBar(
      tabs: ['All', 'Seeds', 'Fertilizers', 'Tools', 'Equipment'],
      controller: controller,
      variant: variant,
      isScrollable: true,
      onTap: onTap,
    );
  }

  static CustomTabBar forDashboard({
    TabController? controller,
    CustomTabBarVariant variant = CustomTabBarVariant.minimal,
    ValueChanged<int>? onTap,
  }) {
    return CustomTabBar(
      tabs: ['Summary', 'Recent', 'Alerts'],
      controller: controller,
      variant: variant,
      onTap: onTap,
    );
  }
}
