import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'dart:math';

class LoanCalculator extends StatefulWidget {
  final double initialAmount;
  final int initialDuration;
  final double interestRate;
  final Function(double amount, int duration, double monthlyPayment)? onCalculate;

  const LoanCalculator({
    super.key,
    this.initialAmount = 0.0,
    this.initialDuration = 12,
    this.interestRate = 8.5,
    this.onCalculate,
  });

  @override
  State<LoanCalculator> createState() => _LoanCalculatorState();
}

class _LoanCalculatorState extends State<LoanCalculator> {
  late TextEditingController _amountController;
  late double _loanAmount;
  late int _duration;
  double _monthlyPayment = 0.0;
  double _totalPayment = 0.0;
  double _totalInterest = 0.0;

  @override
  void initState() {
    super.initState();
    _loanAmount = widget.initialAmount;
    _duration = widget.initialDuration;
    _amountController = TextEditingController(
      text: widget.initialAmount > 0 ? widget.initialAmount.toString() : '',
    );
    _calculateLoan();
  }

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  void _calculateLoan() {
    if (_loanAmount <= 0 || _duration <= 0) {
      setState(() {
        _monthlyPayment = 0.0;
        _totalPayment = 0.0;
        _totalInterest = 0.0;
      });
      return;
    }

    // Convert annual rate to monthly rate
    final monthlyRate = widget.interestRate / 12 / 100;

    // Calculate monthly payment using loan amortization formula
    final monthlyPayment = _loanAmount *
        (monthlyRate * pow(1 + monthlyRate, _duration)) /
        (pow(1 + monthlyRate, _duration) - 1);

    setState(() {
      _monthlyPayment = monthlyPayment;
      _totalPayment = monthlyPayment * _duration;
      _totalInterest = _totalPayment - _loanAmount;
    });

    widget.onCalculate?.call(_loanAmount, _duration, monthlyPayment);
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Loan Calculator',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 2.h),
            TextFormField(
              controller: _amountController,
              decoration: InputDecoration(
                labelText: 'Loan Amount',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.attach_money),
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                setState(() {
                  _loanAmount = double.tryParse(value) ?? 0.0;
                  _calculateLoan();
                });
              },
            ),
            SizedBox(height: 2.h),
            Text(
              'Loan Duration (months):',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Slider(
              value: _duration.toDouble(),
              min: 1,
              max: 60,
              divisions: 59,
              label: '${_duration.round()} months',
              onChanged: (value) {
                setState(() {
                  _duration = value.round();
                  _calculateLoan();
                });
              },
            ),
            SizedBox(height: 2.h),
            Container(
              padding: EdgeInsets.all(3.w),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline,
                ),
              ),
              child: Column(
                children: [
                  _ResultRow(
                    label: 'Monthly Payment:',
                    value: _monthlyPayment,
                  ),
                  Divider(),
                  _ResultRow(
                    label: 'Total Payment:',
                    value: _totalPayment,
                  ),
                  Divider(),
                  _ResultRow(
                    label: 'Total Interest:',
                    value: _totalInterest,
                  ),
                  Divider(),
                  _ResultRow(
                    label: 'Interest Rate:',
                    value: widget.interestRate,
                    isPercentage: true,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ResultRow extends StatelessWidget {
  final String label;
  final double value;
  final bool isPercentage;

  const _ResultRow({
    required this.label,
    required this.value,
    this.isPercentage = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 1.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          Text(
            isPercentage
                ? '${value.toStringAsFixed(1)}%'
                : '₦${value.toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'IBMPlexMono',
                ),
          ),
        ],
      ),
    );
  }
}
